# Redis Cloud Setup Guide

## 🔥 **Quick Setup for Production**

### **Step 1: Create Redis Cloud Account**
1. Go to https://redis.com/redis-enterprise-cloud/
2. Click "Get Started Free"
3. Sign up with your email
4. Verify your email address

### **Step 2: Create Database**
1. After login, click "New Database"
2. Choose "Redis Stack" (recommended for production)
3. Select your cloud provider:
   - **AWS** (recommended for stability)
   - **Google Cloud** 
   - **Azure**
4. Choose region closest to your users:
   - **US East (N. Virginia)** - for US users
   - **Europe (Ireland)** - for EU users
   - **Asia Pacific (Singapore)** - for Asian users

### **Step 3: Database Configuration**
```
Database Name: vendy-data-plans-prod
Memory Limit: 1GB (start here, can scale up)
Eviction Policy: allkeys-lru
Data Persistence: AOF every 1 sec
High Availability: Yes (for production)
```

### **Step 4: Get Connection Details**
After database creation, you'll get:
```
Endpoint: redis-12345.c123.us-east-1-1.ec2.cloud.redislabs.com
Port: 12345
Password: your-secure-password-here
```

### **Step 5: Security Settings**
1. Enable "TLS" for secure connections
2. Set up IP whitelist (add your server IPs)
3. Enable "Auth" with strong password

## 💰 **Pricing Tiers for Your Needs**

### **Free Tier (Testing)**
- 30MB memory
- 30 connections
- Good for development

### **Fixed Plans (Production)**
- **1GB**: $7/month - handles 100K users
- **2.5GB**: $17/month - handles 250K users  
- **5GB**: $34/month - handles 500K users
- **12GB**: $82/month - handles 1M+ users

### **Flexible Plans (Enterprise)**
- Pay per GB used
- Auto-scaling
- 99.99% SLA
- 24/7 support

## 🎯 **Recommended Plan for 1M Users**
**12GB Fixed Plan ($82/month)**
- Handles 1M+ concurrent users
- 1000+ connections
- High availability
- Automatic backups
- 24/7 monitoring

## 🔧 **Connection Testing**
Once you have credentials, test with:
```bash
redis-cli -h your-endpoint -p your-port -a your-password ping
```

Should return: `PONG`

## 📋 **What You Need to Provide**
After setup, give me these 4 values:
1. **REDIS_HOST**: redis-xxxxx.c123.us-east-1-1.ec2.cloud.redislabs.com
2. **REDIS_PORT**: 12345
3. **REDIS_PASSWORD**: your-secure-password
4. **REDIS_TLS**: true (if TLS enabled)

I'll then update your configuration automatically! 🚀
