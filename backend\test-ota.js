/**
 * Test script for OTA updates
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8000'; // Adjust if your server runs on different port

async function testOTAUpdateCheck() {
  try {
    console.log('🧪 Testing OTA Update Check...');
    
    const response = await axios.post(`${BASE_URL}/api/v1/app/updates/check`, {
      currentVersion: "1.0.0",
      currentBuildNumber: "1",
      platform: "android",
      deviceInfo: {
        model: "Test Device",
        systemVersion: "11.0",
        uniqueId: "test-device-123"
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Vendy-android/1.0'
      }
    });

    console.log('✅ Update Check Response:', JSON.stringify(response.data, null, 2));
    
    if (response.data.updateAvailable) {
      console.log('📦 Update available! Testing download...');
      await testOTADownload(response.data.bundle.downloadUrl);
    } else {
      console.log('ℹ️ No update available');
    }
    
  } catch (error) {
    console.error('❌ Update check failed:', error.response?.data || error.message);
  }
}

async function testOTADownload(downloadUrl) {
  try {
    const response = await axios.get(downloadUrl, {
      responseType: 'stream',
      headers: {
        'User-Agent': 'Vendy-android/1.0'
      }
    });

    console.log('✅ Download started successfully');
    console.log('📊 Response headers:', response.headers);
    
    // Don't actually download the full file, just verify it starts
    response.data.destroy();
    console.log('✅ Download test completed');
    
  } catch (error) {
    console.error('❌ Download test failed:', error.response?.data || error.message);
  }
}

async function testOTAAnalytics() {
  try {
    console.log('🧪 Testing OTA Analytics...');
    
    const response = await axios.post(`${BASE_URL}/api/v1/app/updates/analytics`, {
      event: "update_check",
      platform: "android",
      version: "1.0.0",
      buildNumber: "1",
      metadata: {
        deviceInfo: {
          model: "Test Device",
          systemVersion: "11.0"
        }
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Vendy-android/1.0'
      }
    });

    console.log('✅ Analytics Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ Analytics test failed:', error.response?.data || error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting OTA System Tests...\n');
  
  await testOTAUpdateCheck();
  console.log('\n' + '='.repeat(50) + '\n');
  
  await testOTAAnalytics();
  console.log('\n' + '='.repeat(50) + '\n');
  
  console.log('✅ All tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testOTAUpdateCheck,
  testOTADownload,
  testOTAAnalytics,
  runTests
};
