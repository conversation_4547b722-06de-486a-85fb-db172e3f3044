/**
 * International Airtime Controller
 * 
 * Ultra-secure controller for international airtime/data/pin purchases with enhanced
 * validation, security measures, and comprehensive error handling for cross-border transactions.
 * 
 * Enhanced Features:
 * - Multi-layer international validation
 * - Enhanced security for cross-border transactions
 * - Country/operator/variation management
 * - Currency validation and conversion
 * - Comprehensive audit logging
 * - Enhanced fraud detection
 * - Compliance with international regulations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const { body, query, validationResult } = require('express-validator');
const internationalAirtimeService = require('../services/internationalAirtimeService');
const { vtpassErrorHandler } = require('../utils/vtpassErrorHandler');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');
const notificationService = require('../services/notificationService');

/**
 * International Airtime Controller Class
 * Handles all international airtime operations with enhanced security
 */
class InternationalAirtimeController {
  constructor() {
    this.supabase = getSupabase();
  }

  /**
   * Validation rules for international airtime purchase
   */
  static getValidationRules() {
    return [
      body('billersCode')
        .notEmpty()
        .withMessage('Recipient phone number is required')
        .isLength({ min: 8, max: 20 })
        .withMessage('Recipient phone number must be between 8 and 20 digits')
        .matches(/^[\d+\-\s()]+$/)
        .withMessage('Invalid phone number format'),

      body('variation_code')
        .notEmpty()
        .withMessage('Variation code is required')
        .isLength({ min: 1, max: 20 })
        .withMessage('Invalid variation code'),

      body('operator_id')
        .notEmpty()
        .withMessage('Operator ID is required')
        .isNumeric()
        .withMessage('Operator ID must be numeric'),

      body('country_code')
        .notEmpty()
        .withMessage('Country code is required')
        .isLength({ min: 2, max: 2 })
        .withMessage('Country code must be exactly 2 characters')
        .matches(/^[A-Z]{2}$/)
        .withMessage('Country code must be uppercase letters'),

      body('product_type_id')
        .notEmpty()
        .withMessage('Product type ID is required')
        .isNumeric()
        .withMessage('Product type ID must be numeric'),

      body('email')
        .notEmpty()
        .withMessage('Email is required')
        .isEmail()
        .withMessage('Invalid email format')
        .normalizeEmail(),

      body('phone')
        .notEmpty()
        .withMessage('Customer phone is required')
        .isMobilePhone('en-NG')
        .withMessage('Invalid Nigerian phone number format'),

      body('amount')
        .optional()
        .isNumeric()
        .withMessage('Amount must be numeric')
        .custom((value) => {
          if (value && (parseFloat(value) < 0 || parseFloat(value) > 100000)) {
            throw new Error('Amount must be between 0 and 100,000');
          }
          return true;
        }),

      body('pin')
        .optional()
        .isLength({ min: 4, max: 6 })
        .withMessage('PIN must be 4-6 digits')
        .isNumeric()
        .withMessage('PIN must contain only numbers')
    ];
  }

  /**
   * Get all available countries
   * 
   * @route GET /api/v1/international-airtime/countries
   * @access Private
   */
  async getCountries(req, res) {
    const requestId = req.headers['x-request-id'] || `countries_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      logger.info('🌍 [INTERNATIONAL_AIRTIME_CONTROLLER] Countries request:', {
        requestId,
        userId: req.user.id,
        ip: req.ip
      });

      const countries = await internationalAirtimeService.getCountries();

      logger.info('✅ [INTERNATIONAL_AIRTIME_CONTROLLER] Countries retrieved:', {
        requestId,
        userId: req.user.id,
        count: countries.length
      });

      return res.status(200).json({
        success: true,
        data: {
          countries,
          totalCountries: countries.length,
          lastUpdated: new Date().toISOString(),
          requestId
        }
      });
    } catch (error) {
      const errorResponse = vtpassErrorHandler.handleError(error, {
        requestId,
        userId: req.user?.id,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      logger.error('❌ [INTERNATIONAL_AIRTIME_CONTROLLER] Get countries failed:', {
        requestId,
        error: error.message
      });

      return res.status(500).json({
        ...errorResponse,
        requestId
      });
    }
  }

  /**
   * Get product types for a country
   * 
   * @route GET /api/v1/international-airtime/product-types
   * @access Private
   */
  async getProductTypes(req, res) {
    const requestId = req.headers['x-request-id'] || `product_types_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      const { country_code } = req.query;

      if (!country_code) {
        return res.status(400).json({
          success: false,
          message: 'Country code is required',
          code: 'MISSING_COUNTRY_CODE',
          requestId
        });
      }

      logger.info('🌍 [INTERNATIONAL_AIRTIME_CONTROLLER] Product types request:', {
        requestId,
        userId: req.user.id,
        countryCode: country_code,
        ip: req.ip
      });

      const productTypes = await internationalAirtimeService.getProductTypes(country_code);

      logger.info('✅ [INTERNATIONAL_AIRTIME_CONTROLLER] Product types retrieved:', {
        requestId,
        userId: req.user.id,
        countryCode: country_code,
        count: productTypes.length
      });

      return res.status(200).json({
        success: true,
        data: {
          productTypes,
          countryCode: country_code,
          totalTypes: productTypes.length,
          lastUpdated: new Date().toISOString(),
          requestId
        }
      });
    } catch (error) {
      const errorResponse = vtpassErrorHandler.handleError(error, {
        requestId,
        userId: req.user?.id,
        countryCode: req.query?.country_code,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      return res.status(500).json({
        ...errorResponse,
        requestId
      });
    }
  }

  /**
   * Get operators for a country and product type
   * 
   * @route GET /api/v1/international-airtime/operators
   * @access Private
   */
  async getOperators(req, res) {
    const requestId = req.headers['x-request-id'] || `operators_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      const { country_code, product_type_id } = req.query;

      if (!country_code || !product_type_id) {
        return res.status(400).json({
          success: false,
          message: 'Country code and product type ID are required',
          code: 'MISSING_REQUIRED_PARAMETERS',
          requestId
        });
      }

      logger.info('🌍 [INTERNATIONAL_AIRTIME_CONTROLLER] Operators request:', {
        requestId,
        userId: req.user.id,
        countryCode: country_code,
        productTypeId: product_type_id,
        ip: req.ip
      });

      const operators = await internationalAirtimeService.getOperators(country_code, product_type_id);

      logger.info('✅ [INTERNATIONAL_AIRTIME_CONTROLLER] Operators retrieved:', {
        requestId,
        userId: req.user.id,
        countryCode: country_code,
        productTypeId: product_type_id,
        count: operators.length
      });

      return res.status(200).json({
        success: true,
        data: {
          operators,
          countryCode: country_code,
          productTypeId: product_type_id,
          totalOperators: operators.length,
          lastUpdated: new Date().toISOString(),
          requestId
        }
      });
    } catch (error) {
      const errorResponse = vtpassErrorHandler.handleError(error, {
        requestId,
        userId: req.user?.id,
        countryCode: req.query?.country_code,
        productTypeId: req.query?.product_type_id,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      return res.status(500).json({
        ...errorResponse,
        requestId
      });
    }
  }

  /**
   * Get variations for an operator and product type
   * 
   * @route GET /api/v1/international-airtime/variations
   * @access Private
   */
  async getVariations(req, res) {
    const requestId = req.headers['x-request-id'] || `variations_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      const { operator_id, product_type_id } = req.query;

      if (!operator_id || !product_type_id) {
        return res.status(400).json({
          success: false,
          message: 'Operator ID and product type ID are required',
          code: 'MISSING_REQUIRED_PARAMETERS',
          requestId
        });
      }

      logger.info('🌍 [INTERNATIONAL_AIRTIME_CONTROLLER] Variations request:', {
        requestId,
        userId: req.user.id,
        operatorId: operator_id,
        productTypeId: product_type_id,
        ip: req.ip
      });

      const variations = await internationalAirtimeService.getVariations(operator_id, product_type_id);

      logger.info('✅ [INTERNATIONAL_AIRTIME_CONTROLLER] Variations retrieved:', {
        requestId,
        userId: req.user.id,
        operatorId: operator_id,
        productTypeId: product_type_id,
        count: variations.variations?.length || 0
      });

      return res.status(200).json({
        success: true,
        data: {
          ...variations,
          operatorId: operator_id,
          productTypeId: product_type_id,
          totalVariations: variations.variations?.length || 0,
          lastUpdated: new Date().toISOString(),
          requestId
        }
      });
    } catch (error) {
      const errorResponse = vtpassErrorHandler.handleError(error, {
        requestId,
        userId: req.user?.id,
        operatorId: req.query?.operator_id,
        productTypeId: req.query?.product_type_id,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      return res.status(500).json({
        ...errorResponse,
        requestId
      });
    }
  }

  /**
   * Purchase international airtime/data/pin
   *
   * @route POST /api/v1/international-airtime/purchase
   * @access Private (Enhanced security required)
   */
  async purchaseInternationalAirtime(req, res) {
    const requestId = req.headers['x-request-id'] || `intl_purchase_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      logger.info('🌍 [INTERNATIONAL_AIRTIME_CONTROLLER] International purchase initiated:', {
        requestId,
        userId: req.user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        country: req.body.country_code
      });

      // Validate request inputs
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        logger.warn('⚠️ [INTERNATIONAL_AIRTIME_CONTROLLER] Validation failed:', {
          requestId,
          userId: req.user.id,
          errors: errors.array()
        });

        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
          requestId
        });
      }

      const {
        billersCode,
        variation_code,
        operator_id,
        country_code,
        product_type_id,
        email,
        phone,
        amount,
        pin
      } = req.body;

      const userId = req.user.id;

      // Enhanced verification check for international transactions
      if (!req.user.is_email_verified || !req.user.is_phone_verified) {
        logger.warn('🚫 [INTERNATIONAL_AIRTIME_CONTROLLER] Insufficient verification for international transaction:', {
          requestId,
          userId,
          emailVerified: req.user.is_email_verified,
          phoneVerified: req.user.is_phone_verified
        });

        return res.status(403).json({
          success: false,
          message: 'Email and phone verification required for international transactions',
          code: 'INSUFFICIENT_VERIFICATION',
          requestId
        });
      }

      // Verify user PIN if provided (required for high-value international transactions)
      if (pin) {
        const pinValid = await this.verifyUserPin(userId, pin);
        if (!pinValid) {
          logger.warn('🚫 [INTERNATIONAL_AIRTIME_CONTROLLER] Invalid PIN for international transaction:', {
            requestId,
            userId,
            country: country_code,
            ip: req.ip
          });

          return res.status(401).json({
            success: false,
            message: 'Invalid PIN',
            code: 'INVALID_PIN',
            requestId
          });
        }
      }

      // Check user balance
      const userBalance = await this.getUserBalance(userId);
      const transactionAmount = parseFloat(amount) || 0;

      if (userBalance < transactionAmount) {
        logger.warn('💰 [INTERNATIONAL_AIRTIME_CONTROLLER] Insufficient balance for international transaction:', {
          requestId,
          userId,
          balance: userBalance,
          requestedAmount: transactionAmount,
          country: country_code
        });

        return res.status(400).json({
          success: false,
          message: 'Insufficient balance for international transaction',
          code: 'INSUFFICIENT_BALANCE',
          currentBalance: userBalance,
          requiredAmount: transactionAmount,
          requestId
        });
      }

      // Prepare purchase data with enhanced metadata
      const purchaseData = {
        userId,
        billersCode,
        variation_code,
        operator_id,
        country_code,
        product_type_id,
        email,
        phone,
        amount: transactionAmount,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        requestId,
        riskScore: req.internationalRiskScore || 0,
        riskFactors: req.internationalRiskFactors || []
      };

      logger.info('💳 [INTERNATIONAL_AIRTIME_CONTROLLER] Processing international purchase:', {
        requestId,
        userId,
        country: country_code,
        operator: operator_id,
        amount: transactionAmount,
        recipient: billersCode?.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3'),
        riskScore: purchaseData.riskScore
      });

      // Process the purchase through international airtime service
      const result = await internationalAirtimeService.purchaseInternationalAirtime(purchaseData);

      // Send success notification
      if (result.success) {
        await this.sendInternationalPurchaseNotification(userId, {
          type: 'international_airtime',
          amount: transactionAmount,
          recipient: billersCode,
          country: country_code,
          countryName: result.countryInfo?.name,
          transactionId: result.transactionId
        });
      }

      logger.info('✅ [INTERNATIONAL_AIRTIME_CONTROLLER] International purchase completed:', {
        requestId,
        userId,
        transactionId: result.transactionId,
        status: result.status,
        success: result.success,
        country: country_code
      });

      return res.status(200).json({
        success: result.success,
        message: result.message,
        data: {
          transactionId: result.transactionId,
          status: result.status,
          amount: transactionAmount,
          recipient: billersCode,
          country: country_code,
          countryName: result.countryInfo?.name,
          operatorAmount: result.countryInfo?.operatorAmount,
          requestId,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      // Handle errors using the comprehensive error handler
      const errorResponse = vtpassErrorHandler.handleError(error, {
        requestId,
        userId: req.user?.id,
        country: req.body?.country_code,
        amount: req.body?.amount,
        recipient: req.body?.billersCode,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      logger.error('❌ [INTERNATIONAL_AIRTIME_CONTROLLER] International purchase failed:', {
        requestId,
        userId: req.user?.id,
        country: req.body?.country_code,
        error: error.message,
        stack: error.stack
      });

      return res.status(500).json({
        ...errorResponse,
        requestId
      });
    }
  }

  /**
   * Get user's current balance
   */
  async getUserBalance(userId) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('balance')
        .eq('id', userId)
        .single();

      if (error) {
        logger.error('❌ [INTERNATIONAL_AIRTIME_CONTROLLER] Balance query error:', error);
        throw new Error('Failed to retrieve user balance');
      }

      return parseFloat(data.balance) || 0;
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_CONTROLLER] Get balance error:', error);
      throw error;
    }
  }

  /**
   * Verify user PIN for additional security
   */
  async verifyUserPin(userId, pin) {
    try {
      const bcrypt = require('bcryptjs');

      const { data, error } = await this.supabase
        .from('users')
        .select('pin')
        .eq('id', userId)
        .single();

      if (error || !data) {
        logger.error('❌ [INTERNATIONAL_AIRTIME_CONTROLLER] PIN verification query error:', error);
        return false;
      }

      return await bcrypt.compare(pin, data.pin);
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_CONTROLLER] PIN verification error:', error);
      return false;
    }
  }

  /**
   * Send international purchase notification
   */
  async sendInternationalPurchaseNotification(userId, purchaseData) {
    try {
      await notificationService.sendPurchaseNotification(userId, {
        ...purchaseData,
        isInternational: true
      });

      logger.info('📱 [INTERNATIONAL_AIRTIME_CONTROLLER] International notification sent:', {
        userId,
        type: purchaseData.type,
        country: purchaseData.country,
        amount: purchaseData.amount
      });
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_CONTROLLER] Notification error:', error);
      // Don't throw error as this is not critical for the transaction
    }
  }

  /**
   * Get user's international transaction history
   *
   * @route GET /api/v1/international-airtime/history
   * @access Private
   */
  async getInternationalHistory(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 20, status, country_code } = req.query;
      const offset = (page - 1) * limit;

      let query = this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .eq('type', 'international_airtime')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (status) {
        query = query.eq('status', status);
      }

      if (country_code) {
        query = query.contains('metadata', { country_code });
      }

      const { data, error } = await query;

      if (error) {
        logger.error('❌ [INTERNATIONAL_AIRTIME_CONTROLLER] History query error:', error);
        return res.status(500).json({
          success: false,
          message: 'Failed to retrieve international transaction history'
        });
      }

      return res.status(200).json({
        success: true,
        data: {
          transactions: data,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: data.length
          }
        }
      });
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_CONTROLLER] Get history error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve international transaction history'
      });
    }
  }
}

// Create singleton instance
const internationalAirtimeController = new InternationalAirtimeController();

module.exports = {
  getCountries: internationalAirtimeController.getCountries.bind(internationalAirtimeController),
  getProductTypes: internationalAirtimeController.getProductTypes.bind(internationalAirtimeController),
  getOperators: internationalAirtimeController.getOperators.bind(internationalAirtimeController),
  getVariations: internationalAirtimeController.getVariations.bind(internationalAirtimeController),
  purchaseInternationalAirtime: internationalAirtimeController.purchaseInternationalAirtime.bind(internationalAirtimeController),
  getInternationalHistory: internationalAirtimeController.getInternationalHistory.bind(internationalAirtimeController),
  getValidationRules: InternationalAirtimeController.getValidationRules,
  internationalAirtimeController
};
