#!/usr/bin/env node

/**
 * Simple OTA Update Deployment Script
 * 
 * This script automates the process of creating and deploying OTA updates.
 * It builds bundles, creates packages, and updates the backend configuration.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// Configuration
const config = {
  bundlesDir: './ota-bundles',
  backendBundlesDir: './backend/storage/app-bundles',
  configFile: './backend/config/app-updates.json'
};

// Helper functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  warning: (msg) => console.log(`⚠️  ${msg}`),
  error: (msg) => console.log(`❌ ${msg}`)
};

// Parse command line arguments
const args = process.argv.slice(2);
const getArg = (flag) => {
  const index = args.indexOf(flag);
  return index !== -1 ? args[index + 1] : null;
};

const version = getArg('-v') || getArg('--version');
const buildNumber = getArg('-b') || getArg('--build');
const description = getArg('-d') || getArg('--description');
const isMandatory = args.includes('-m') || args.includes('--mandatory');
const platformsArg = getArg('-p') || getArg('--platform') || 'android,ios';
const platforms = platformsArg.split(',');

// Validate arguments
if (!version || !buildNumber || !description) {
  console.log(`
Usage: node deploy-update.js -v VERSION -b BUILD_NUMBER -d DESCRIPTION [OPTIONS]

Options:
  -v, --version      Version number (e.g., 1.0.2)
  -b, --build        Build number (e.g., 3)
  -d, --description  Update description
  -m, --mandatory    Mark as mandatory update
  -p, --platform     Platforms (android,ios or android or ios)

Example:
  node deploy-update.js -v 1.0.2 -b 3 -d "Bug fixes and UI improvements" -m
  `);
  process.exit(1);
}

// Create directories
const createDirectories = () => {
  if (!fs.existsSync(config.bundlesDir)) {
    fs.mkdirSync(config.bundlesDir, { recursive: true });
  }
  if (!fs.existsSync(config.backendBundlesDir)) {
    fs.mkdirSync(config.backendBundlesDir, { recursive: true });
  }
};

// Build bundle for platform
const buildPlatformBundle = (platform) => {
  log.info(`Building ${platform} bundle...`);
  
  const platformDir = path.join(config.bundlesDir, platform);
  if (!fs.existsSync(platformDir)) {
    fs.mkdirSync(platformDir, { recursive: true });
  }
  
  try {
    // Build the bundle
    execSync(`npx react-native bundle \
      --platform ${platform} \
      --dev false \
      --entry-file index.js \
      --bundle-output ${platformDir}/bundle.js \
      --assets-dest ${platformDir}/assets \
      --reset-cache`, { stdio: 'inherit' });
    
    log.success(`${platform} bundle created successfully`);
    
    // Create zip file
    const zipName = `vendy-v${version}-${platform}.zip`;
    const zipPath = path.join(config.bundlesDir, zipName);
    
    process.chdir(platformDir);
    execSync(`zip -r ../${zipName} bundle.js assets/`, { stdio: 'pipe' });
    process.chdir(path.resolve(__dirname));
    
    if (fs.existsSync(zipPath)) {
      log.success(`${platform} zip package created: ${zipName}`);
      
      // Calculate checksum and size
      const fileBuffer = fs.readFileSync(zipPath);
      const checksum = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      const size = fs.statSync(zipPath).size;
      
      log.info(`${platform} checksum: sha256:${checksum}`);
      log.info(`${platform} size: ${size} bytes`);
      
      // Copy to backend storage
      fs.copyFileSync(zipPath, path.join(config.backendBundlesDir, zipName));
      log.success(`${platform} bundle copied to backend storage`);
      
      return {
        platform,
        filename: zipName,
        checksum: `sha256:${checksum}`,
        size
      };
    } else {
      throw new Error(`Failed to create ${platform} zip package`);
    }
  } catch (error) {
    log.error(`Failed to build ${platform} bundle: ${error.message}`);
    throw error;
  }
};

// Update backend configuration
const updateBackendConfig = (bundleInfos) => {
  log.info('Updating backend configuration...');
  
  // Create backup
  if (fs.existsSync(config.configFile)) {
    fs.copyFileSync(config.configFile, `${config.configFile}.backup`);
  }
  
  // Read existing config or create new one
  let existingConfig;
  try {
    existingConfig = JSON.parse(fs.readFileSync(config.configFile, 'utf8'));
  } catch (error) {
    existingConfig = {
      lastUpdated: '',
      updates: [],
      config: { enableUpdates: true }
    };
  }
  
  const timestamp = new Date().toISOString();
  
  // Create new update entries
  const newUpdates = bundleInfos.map(info => ({
    id: `vendy-v${version}-${info.platform}`,
    version,
    buildNumber,
    platform: info.platform,
    filename: info.filename,
    downloadUrl: `/api/v1/app/updates/download/vendy-v${version}-${info.platform}`,
    checksum: info.checksum,
    size: info.size,
    isMandatory,
    description,
    releaseNotes: description,
    minAppVersion: '1.0.0',
    maxAppVersion: null,
    createdAt: timestamp,
    deployedAt: timestamp,
    isActive: true
  }));
  
  // Update configuration
  const updatedConfig = {
    ...existingConfig,
    lastUpdated: timestamp,
    updates: [...newUpdates, ...existingConfig.updates]
  };
  
  // Write updated configuration
  fs.writeFileSync(config.configFile, JSON.stringify(updatedConfig, null, 2));
  log.success('Backend configuration updated successfully');
};

// Clean up temporary files
const cleanup = () => {
  if (fs.existsSync(config.bundlesDir)) {
    fs.rmSync(config.bundlesDir, { recursive: true, force: true });
  }
};

// Main deployment function
const deployUpdate = async () => {
  try {
    log.info(`Starting OTA update deployment for version ${version} (build ${buildNumber})`);
    
    createDirectories();
    
    const bundleInfos = [];
    
    // Build bundles for each platform
    for (const platform of platforms) {
      const bundleInfo = buildPlatformBundle(platform);
      bundleInfos.push(bundleInfo);
    }
    
    // Update backend configuration
    updateBackendConfig(bundleInfos);
    
    // Clean up
    cleanup();
    
    // Summary
    log.success('🎉 OTA Update deployment completed!');
    console.log('');
    log.info('📋 Summary:');
    console.log(`   Version: ${version}`);
    console.log(`   Build: ${buildNumber}`);
    console.log(`   Platforms: ${platforms.join(', ')}`);
    console.log(`   Mandatory: ${isMandatory}`);
    console.log(`   Description: ${description}`);
    console.log('');
    log.info('📱 Next steps:');
    console.log('   1. Restart your backend server to load the new configuration');
    console.log('   2. Test the update on a development device');
    console.log('   3. Monitor update analytics in your backend logs');
    console.log('');
    log.warning('⚠️  Remember to restart your backend server:');
    console.log('   pm2 restart vendy-backend');
    console.log('   # or');
    console.log('   npm run start');
    
  } catch (error) {
    log.error(`Deployment failed: ${error.message}`);
    cleanup();
    process.exit(1);
  }
};

// Run deployment
deployUpdate();
