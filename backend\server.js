const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss');
const hpp = require('hpp');
require('dotenv').config();

const { connectDB } = require('./config/database');
const errorHandler = require('./middleware/errorHandler');
const logger = require('./utils/logger');

const app = express();

// Connect to database and start the rest of the app only after DB is ready
connectDB().then(async () => {
  // Now require services and routes that depend on Supabase
  const realtimeService = require('./services/realtimeService');
  // dataPlansWebSocketService is now handled by data-plan-manager module
  const userExistenceMiddleware = require('./middleware/userExistenceMiddleware');

  // Import routes
  const authRoutes = require('./routes/auth');
  const googleAuthRoutes = require('./routes/googleAuth');
  const userRoutes = require('./routes/user');
  const smsRoutes = require('./routes/sms');
  const emailRoutes = require('./routes/email');
  const setupRoutes = require('./routes/setup');
  const featureFlagRoutes = require('./routes/featureFlags');
  const networkDetect = require('./routes/networkDetect');
  const notificationRoutes = require('./routes/notification');
  const healthRoutes = require('./routes/health');
  // dataPlansRoutes handled by data-plan-manager module
  const adminRoutes = require('./routes/admin');
  const crashReportsRoutes = require('./routes/crashReports');
  // const verifyPinRoutes = require('./routes/verifyPin');

  // Initialize realtime service for user deletion detection
  realtimeService.initialize().then(() => {
    logger.info('Realtime service initialized successfully');
  }).catch(error => {
    logger.error('Realtime service initialization failed:', error);
  });

  // WhatsApp Bot Module has been migrated to a standalone service
  // See: whatsapp-bot-service/ for the independent WhatsApp bot implementation

  // Initialize Data Plan Manager Module
  let dataPlanManagerModule = null;
  try {
    logger.info('🚀 [SERVER] Loading Data Plan Manager Module...');
    dataPlanManagerModule = require('./data-plan-manager');
    logger.info('✅ [SERVER] Data Plan Manager Module loaded successfully');
    logger.info('📊 [SERVER] Data Plan Manager Module type:', typeof dataPlanManagerModule);
    logger.info('📊 [SERVER] Data Plan Manager Module methods:', Object.getOwnPropertyNames(dataPlanManagerModule));
  } catch (error) {
    logger.error('❌ [SERVER] Data Plan Manager Module initialization failed:', error);
    // Continue without Data Plan Manager if initialization fails
    logger.info('🔄 [SERVER] Continuing server startup without Data Plan Manager...');
  }

  logger.info('🚀 [SERVER] Setting up middleware and routes...');



  // Add request logging middleware
  app.use((req, res, next) => {
    console.log(`🌐 [SERVER] ${req.method} ${req.originalUrl}`);
    console.log('📋 Headers:', JSON.stringify(req.headers, null, 2));
    if (req.body && Object.keys(req.body).length > 0) {
      console.log('📦 Body:', JSON.stringify(req.body, null, 2));
    }
    next();
  });

  // Security blacklist middleware (must be early in the chain)
  const { securityBlacklist } = require('./middleware/securityBlacklist');
  const { ipBlockingMiddleware } = require('./middleware/ipBlocking');
  app.use(ipBlockingMiddleware);
  app.use(securityBlacklist);

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  }));

  // CORS configuration
  const corsOptions = {
    origin: function (origin, callback) {
      const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];

      // Log CORS request for debugging
      if (process.env.NODE_ENV === 'development') {
        logger.info(`🌐 CORS Request - Origin: ${origin}, Allowed: ${allowedOrigins.join(', ')}`);
      }

      if (!origin || allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        logger.warn(`❌ CORS blocked - Origin: ${origin} not in allowed origins: ${allowedOrigins.join(', ')}`);
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    optionsSuccessStatus: 200
  };
  app.use(cors(corsOptions));

  // Trust proxy for rate limiting (required for tunnels and proxies)
  // Use specific proxy configuration for better security
  app.set('trust proxy', 1);

  // Rate limiting
  const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
    message: {
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000) / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use('/api/', limiter);

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Data sanitization
  app.use(mongoSanitize()); // Against NoSQL injection
  app.use(hpp()); // Prevent parameter pollution

  // Compression middleware
  app.use(compression());

  // Logging middleware
  if (process.env.NODE_ENV === 'development') {
    app.use(morgan('dev'));
  } else {
    app.use(morgan('combined', {
      stream: {
        write: (message) => logger.info(message.trim())
      }
    }));
  }

  // Health check endpoints
  app.use('/health', healthRoutes);
  app.use(`/api/${process.env.API_VERSION || 'v1'}/health`, healthRoutes);

  // API routes
  app.use(`/api/${process.env.API_VERSION || 'v1'}/auth`, authRoutes);
  app.use(`/api/${process.env.API_VERSION || 'v1'}/auth`, googleAuthRoutes);

  // User validation routes (for security validation)
  const userValidationRoutes = require('./routes/userValidation');
  app.use(`/api/${process.env.API_VERSION || 'v1'}/auth`, userValidationRoutes);
  // app.use(`/api/v1/auth`, verifyPinRoutes);

  // Apply user existence middleware to authenticated routes
  app.use(`/api/${process.env.API_VERSION || 'v1'}/user`, userExistenceMiddleware.checkUserExists, userRoutes);
  app.use(`/api/${process.env.API_VERSION || 'v1'}/sms`, userExistenceMiddleware.checkUserExists, smsRoutes);
  app.use(`/api/${process.env.API_VERSION || 'v1'}/email`, emailRoutes); // Email routes handle their own auth
  app.use(`/api/${process.env.API_VERSION || 'v1'}/setup`, userExistenceMiddleware.checkUserExists, setupRoutes);
  app.use(`/api/${process.env.API_VERSION || 'v1'}`, userExistenceMiddleware.checkUserExists, featureFlagRoutes);
  app.use('/api/network-detect', networkDetect);
  app.use(`/api/${process.env.API_VERSION || 'v1'}/notification`, userExistenceMiddleware.checkUserExists, notificationRoutes);
  app.use(`/api/${process.env.API_VERSION || 'v1'}/admin`, adminRoutes);

  // Data Plans routes handled by data-plan-manager module
  app.use(`/api/${process.env.API_VERSION || 'v1'}/crash-reports`, userExistenceMiddleware.checkUserExists, crashReportsRoutes);

  // VTpass API routes (airtime and transactions)
  const airtimeRoutes = require('./routes/airtime');
  const transactionRoutes = require('./routes/transactions');
  const internationalAirtimeRoutes = require('./routes/internationalAirtime');
  app.use(`/api/${process.env.API_VERSION || 'v1'}/airtime`, airtimeRoutes);
  app.use(`/api/${process.env.API_VERSION || 'v1'}/transactions`, transactionRoutes);
  app.use(`/api/${process.env.API_VERSION || 'v1'}/international-airtime`, internationalAirtimeRoutes);

  // App Updates (OTA) routes
  const appUpdatesRoutes = require('./routes/appUpdates');
  const otaAdminRoutes = require('./routes/admin/otaUpdates');
  app.use(`/api/${process.env.API_VERSION || 'v1'}/app/updates`, appUpdatesRoutes);
  app.use(`/api/${process.env.API_VERSION || 'v1'}/admin/ota`, otaAdminRoutes);

  // Also add routes without version for backward compatibility
  app.use(`/api/auth`, authRoutes);
  app.use(`/api/auth`, googleAuthRoutes);
  app.use(`/api/user`, userExistenceMiddleware.checkUserExists, userRoutes);
  app.use(`/api/sms`, userExistenceMiddleware.checkUserExists, smsRoutes);
  app.use(`/api/email`, emailRoutes); // Email routes handle their own auth
  app.use(`/api/setup`, userExistenceMiddleware.checkUserExists, setupRoutes);
  app.use(`/api/crash-reports`, userExistenceMiddleware.checkUserExists, crashReportsRoutes);
  app.use(`/api/admin`, adminRoutes); // Admin routes backward compatibility
  app.use(`/api/admin/ota`, otaAdminRoutes); // OTA admin routes backward compatibility
  app.use(`/api/app/updates`, appUpdatesRoutes); // App updates backward compatibility

  // Note: 404 handler will be registered after data plan manager initialization

  // Global error handler
  app.use(errorHandler);

  const PORT = process.env.PORT || 3000;

  logger.info('🚀 [SERVER] Starting server...');
  const server = app.listen(PORT, async () => {
    logger.info(`✅ [SERVER] Vendy API server running on port ${PORT} in ${process.env.NODE_ENV} mode`);

    // Initialize Data Plan Manager after server starts
    if (dataPlanManagerModule) {
      try {
        logger.info('🚀 [SERVER] Initializing Data Plan Manager...');
        await dataPlanManagerModule.initialize(app, server);
        logger.info('✅ [SERVER] Data Plan Manager initialized successfully');

        // Test if routes are registered
        logger.info('🧪 [SERVER] Testing route registration...');
        const routes = app._router.stack.filter(r => r.regexp && r.regexp.source.includes('data-plans'));
        logger.info('📊 [SERVER] Data plans routes found:', routes.length);

        // List all routes for debugging
        logger.info('📊 [SERVER] All registered routes:');
        app._router.stack.forEach((layer, index) => {
          if (layer.route) {
            logger.info(`  ${index}: ${layer.route.path} (${Object.keys(layer.route.methods).join(', ')})`);
          } else if (layer.regexp) {
            logger.info(`  ${index}: ${layer.regexp.source} (middleware)`);
          }
        });

      } catch (error) {
        logger.error('❌ [SERVER] Failed to initialize Data Plan Manager:', error.message);
        logger.error('❌ [SERVER] Full error:', error);
      }
    } else {
      logger.warn('⚠️ [SERVER] Data Plan Manager Module not available');
    }

    // WebSocket service for data plans is now handled by the data-plan-manager module
    // The realTimeUpdateService in data-plan-manager provides WebSocket functionality
    logger.info('ℹ️ [SERVER] WebSocket service handled by data-plan-manager module');

    // Register 404 handler AFTER all routes are registered
    app.all('*', (req, res) => {
      res.status(404).json({
        status: 'error',
        message: `Route ${req.originalUrl} not found`
      });
    });

    logger.info('🎉 [SERVER] Server startup completed successfully!');
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    logger.info('SIGTERM received. Shutting down gracefully...');
    server.close(async () => {
      // Cleanup realtime service
      await realtimeService.cleanup();
      // WebSocket service cleanup is handled by data-plan-manager module
      // Cleanup Data Plan Manager module if it was initialized
      if (dataPlanManagerModule && typeof dataPlanManagerModule.shutdown === 'function') {
        await dataPlanManagerModule.shutdown();
      }
      logger.info('Process terminated');
      process.exit(0);
    });
  });

  process.on('SIGINT', () => {
    logger.info('SIGINT received. Shutting down gracefully...');
    server.close(async () => {
      // Cleanup realtime service
      await realtimeService.cleanup();
      // Cleanup Data Plan Manager module if it was initialized
      if (dataPlanManagerModule && typeof dataPlanManagerModule.shutdown === 'function') {
        await dataPlanManagerModule.shutdown();
      }
      logger.info('Process terminated');
      process.exit(0);
    });
  });

}).catch((err) => {
  logger.error('❌ [SERVER] Failed to initialize server:', err);
  logger.error('❌ [SERVER] Error details:', {
    message: err.message,
    stack: err.stack,
    name: err.name
  });
  console.error('❌ [SERVER] Full error object:', err);
  process.exit(1);
});
