const mongoose = require('mongoose');

/**
 * OTA Analytics Schema
 * Tracks all OTA update related events and analytics
 */
const otaAnalyticsSchema = new mongoose.Schema({
  // Event Information
  event: {
    type: String,
    required: true,
    enum: [
      'update_check', 'download_started', 'download_completed', 'download_failed',
      'install_started', 'install_completed', 'install_failed', 'rollback_started',
      'rollback_completed', 'restart_required', 'user_dismissed'
    ]
  },
  
  // Update Information
  updateId: {
    type: String,
    index: true
  },
  version: String,
  buildNumber: String,
  platform: {
    type: String,
    required: true,
    enum: ['ios', 'android'],
    index: true
  },
  
  // User/Device Information
  userId: {
    type: String,
    index: true
  },
  deviceId: String,
  userAgent: String,
  ip: String,
  deviceInfo: {
    model: String,
    systemVersion: String,
    appVersion: String,
    manufacturer: String,
    isEmulator: Boolean
  },
  
  // Event Metadata
  metadata: mongoose.Schema.Types.Mixed,
  error: {
    message: String,
    code: String,
    stack: String
  },
  
  // Performance Metrics
  duration: Number, // in milliseconds
  downloadSize: Number, // in bytes
  installSize: Number, // in bytes
  
  // Timestamps
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // Geolocation (optional)
  location: {
    country: String,
    region: String,
    city: String
  }
}, {
  timestamps: true,
  collection: 'ota_analytics'
});

// Indexes for performance
otaAnalyticsSchema.index({ event: 1, timestamp: -1 });
otaAnalyticsSchema.index({ updateId: 1, event: 1 });
otaAnalyticsSchema.index({ platform: 1, timestamp: -1 });
otaAnalyticsSchema.index({ userId: 1, timestamp: -1 });

/**
 * Static methods for analytics queries
 */
otaAnalyticsSchema.statics.getUpdateStats = async function(updateId, timeRange = 7) {
  const startDate = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);
  
  const pipeline = [
    {
      $match: {
        updateId,
        timestamp: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$event',
        count: { $sum: 1 },
        avgDuration: { $avg: '$duration' },
        totalSize: { $sum: '$downloadSize' }
      }
    }
  ];
  
  return this.aggregate(pipeline);
};

otaAnalyticsSchema.statics.getPlatformStats = async function(timeRange = 30) {
  const startDate = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        timestamp: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          platform: '$platform',
          event: '$event'
        },
        count: { $sum: 1 }
      }
    },
    {
      $group: {
        _id: '$_id.platform',
        events: {
          $push: {
            event: '$_id.event',
            count: '$count'
          }
        },
        totalEvents: { $sum: '$count' }
      }
    }
  ]);
};

otaAnalyticsSchema.statics.getSuccessRates = async function(timeRange = 7) {
  const startDate = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        timestamp: { $gte: startDate },
        event: { $in: ['download_completed', 'download_failed', 'install_completed', 'install_failed'] }
      }
    },
    {
      $group: {
        _id: {
          updateId: '$updateId',
          type: {
            $cond: {
              if: { $in: ['$event', ['download_completed', 'download_failed']] },
              then: 'download',
              else: 'install'
            }
          },
          success: {
            $cond: {
              if: { $in: ['$event', ['download_completed', 'install_completed']] },
              then: true,
              else: false
            }
          }
        },
        count: { $sum: 1 }
      }
    },
    {
      $group: {
        _id: {
          updateId: '$_id.updateId',
          type: '$_id.type'
        },
        total: { $sum: '$count' },
        successful: {
          $sum: {
            $cond: {
              if: '$_id.success',
              then: '$count',
              else: 0
            }
          }
        }
      }
    },
    {
      $project: {
        updateId: '$_id.updateId',
        type: '$_id.type',
        total: 1,
        successful: 1,
        successRate: {
          $multiply: [
            { $divide: ['$successful', '$total'] },
            100
          ]
        }
      }
    }
  ]);
};

module.exports = mongoose.model('OTAAnalytics', otaAnalyticsSchema);
