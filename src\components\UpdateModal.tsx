/**
 * Update Modal Component
 * 
 * Displays OTA update notifications, progress, and user interactions.
 * Provides a clean, user-friendly interface for managing app updates.
 * 
 * Features:
 * - Update availability notifications
 * - Download progress indicators
 * - Installation progress
 * - Mandatory vs optional update handling
 * - Professional UI with glass morphism design
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Alert,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useCodePush } from '../store';
import otaService from '../services/codePushService';
import logger from '../services/productionLogger';

const { width, height } = Dimensions.get('window');

interface UpdateModalProps {
  visible: boolean;
  onClose: () => void;
}

const UpdateModal: React.FC<UpdateModalProps> = ({ visible, onClose }) => {
  const { updateStatus, updateProgress, updateInfo } = useCodePush();
  const [isDownloading, setIsDownloading] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);

  useEffect(() => {
    setIsDownloading(updateStatus === 'downloading');
    setIsInstalling(updateStatus === 'installing');
  }, [updateStatus]);

  const handleInstallUpdate = async () => {
    try {
      logger.info('🔄 [UPDATE MODAL] User initiated update installation', null, 'ui');
      
      if (updateStatus === 'update_available') {
        // Download and install
        await otaService.downloadUpdate();
      } else {
        // Just install if already downloaded
        await otaService.installUpdate();
      }
    } catch (error) {
      logger.error('❌ [UPDATE MODAL] Update installation failed', error, 'ui');
      Alert.alert(
        'Update Failed',
        'Failed to install the update. Please try again later.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleLater = () => {
    logger.info('⏰ [UPDATE MODAL] User chose to update later', null, 'ui');
    onClose();
  };

  const getModalContent = () => {
    switch (updateStatus) {
      case 'checking':
        return (
          <View style={styles.content}>
            <ActivityIndicator size="large" color="#8B5CF6" />
            <Text style={styles.title}>Checking for Updates</Text>
            <Text style={styles.description}>
              Please wait while we check for the latest version...
            </Text>
          </View>
        );

      case 'update_available':
        return (
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Text style={styles.updateIcon}>📱</Text>
            </View>
            <Text style={styles.title}>Update Available</Text>
            <Text style={styles.version}>Version {updateInfo?.version}</Text>
            <Text style={styles.description}>
              {updateInfo?.description || 'A new version of the app is available with improvements and bug fixes.'}
            </Text>
            
            {updateInfo?.packageSize && (
              <Text style={styles.sizeText}>
                Size: {(updateInfo.packageSize / (1024 * 1024)).toFixed(1)} MB
              </Text>
            )}

            <View style={styles.buttonContainer}>
              {!updateInfo?.isMandatory && (
                <TouchableOpacity style={styles.laterButton} onPress={handleLater}>
                  <Text style={styles.laterButtonText}>Later</Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity style={styles.installButton} onPress={handleInstallUpdate}>
                <LinearGradient
                  colors={['#8B5CF6', '#7C3AED']}
                  style={styles.installButtonGradient}
                >
                  <Text style={styles.installButtonText}>
                    {updateInfo?.isMandatory ? 'Install Now' : 'Install'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        );

      case 'downloading':
        return (
          <View style={styles.content}>
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: `${updateProgress}%` }]} />
              </View>
              <Text style={styles.progressText}>{updateProgress}%</Text>
            </View>
            <Text style={styles.title}>Downloading Update</Text>
            <Text style={styles.description}>
              Please wait while the update is being downloaded...
            </Text>
          </View>
        );

      case 'installing':
        return (
          <View style={styles.content}>
            <ActivityIndicator size="large" color="#8B5CF6" />
            <Text style={styles.title}>Installing Update</Text>
            <Text style={styles.description}>
              The update is being installed. This may take a few moments...
            </Text>
          </View>
        );

      case 'restart_required':
        return (
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Text style={styles.updateIcon}>🔄</Text>
            </View>
            <Text style={styles.title}>Restart Required</Text>
            <Text style={styles.description}>
              The update has been installed successfully. The app will restart to apply the changes.
            </Text>
            <TouchableOpacity style={styles.installButton} onPress={() => {
              // In a real implementation, you'd restart the app here
              logger.info('🔄 [UPDATE MODAL] User confirmed app restart', null, 'ui');
              Alert.alert('Restart', 'The app will restart now to apply the update.');
            }}>
              <LinearGradient
                colors={['#8B5CF6', '#7C3AED']}
                style={styles.installButtonGradient}
              >
                <Text style={styles.installButtonText}>Restart Now</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        );

      case 'error':
        return (
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Text style={styles.errorIcon}>⚠️</Text>
            </View>
            <Text style={styles.title}>Update Failed</Text>
            <Text style={styles.description}>
              There was an error checking for updates. Please try again later.
            </Text>
            <TouchableOpacity style={styles.installButton} onPress={onClose}>
              <LinearGradient
                colors={['#8B5CF6', '#7C3AED']}
                style={styles.installButtonGradient}
              >
                <Text style={styles.installButtonText}>OK</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        );

      default:
        return null;
    }
  };

  if (!visible || !updateStatus || updateStatus === 'up_to_date') {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={updateInfo?.isMandatory ? undefined : onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <LinearGradient
            colors={['rgba(139, 92, 246, 0.1)', 'rgba(124, 58, 237, 0.1)']}
            style={styles.modalGradient}
          >
            {getModalContent()}
          </LinearGradient>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modal: {
    width: width * 0.9,
    maxWidth: 400,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
  },
  modalGradient: {
    padding: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(10px)',
  },
  content: {
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 20,
  },
  updateIcon: {
    fontSize: 48,
  },
  errorIcon: {
    fontSize: 48,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  version: {
    fontSize: 16,
    color: '#8B5CF6',
    fontWeight: '600',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  sizeText: {
    fontSize: 14,
    color: '#9CA3AF',
    marginBottom: 24,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#8B5CF6',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#8B5CF6',
    fontWeight: '600',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 12,
  },
  laterButton: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  laterButtonText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '600',
  },
  installButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  installButtonGradient: {
    paddingVertical: 14,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  installButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});

export default UpdateModal;
