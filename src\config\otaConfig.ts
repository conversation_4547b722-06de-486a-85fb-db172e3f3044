/**
 * OTA Update Configuration
 * 
 * This file contains different update policies and configurations
 * for the OTA update system. You can easily switch between different
 * update behaviors by changing the exported configuration.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { UpdatePolicy } from '../services/codePushService';

// ============================================================================
// UPDATE POLICY PRESETS
// ============================================================================

/**
 * AUTOMATIC SILENT UPDATES
 * - Downloads and installs automatically
 * - No user interaction required
 * - Best for minor updates and bug fixes
 */
export const AUTOMATIC_SILENT_CONFIG = {
  checkFrequency: 'on_app_resume' as const,
  updatePolicy: UpdatePolicy.SILENT,
  autoDownload: true,
  autoInstall: true,
  minimumBackgroundDuration: 10000,
  enableAutoCheck: true,
  enableAnalytics: true,
  updateCheckInterval: 30 * 60 * 1000, // 30 minutes
};

/**
 * SMART AUTOMATIC UPDATES
 * - Downloads automatically
 * - Installs when app resumes from background
 * - Good balance between automation and user experience
 */
export const SMART_AUTOMATIC_CONFIG = {
  checkFrequency: 'on_app_resume' as const,
  updatePolicy: UpdatePolicy.ON_APP_RESUME,
  autoDownload: true,
  autoInstall: true,
  minimumBackgroundDuration: 10000,
  enableAutoCheck: true,
  enableAnalytics: true,
  updateCheckInterval: 30 * 60 * 1000,
};

/**
 * MANUAL UPDATES
 * - User must approve downloads and installations
 * - Shows update modal with description
 * - Best for major updates or when user control is important
 */
export const MANUAL_CONFIG = {
  checkFrequency: 'on_app_resume' as const,
  updatePolicy: UpdatePolicy.MANUAL,
  autoDownload: false,
  autoInstall: false,
  minimumBackgroundDuration: 10000,
  enableAutoCheck: true,
  enableAnalytics: true,
  updateCheckInterval: 30 * 60 * 1000,
};

/**
 * IMMEDIATE UPDATES
 * - Downloads and installs immediately
 * - App restarts right away
 * - Use only for critical security fixes
 */
export const IMMEDIATE_CONFIG = {
  checkFrequency: 'on_app_start' as const,
  updatePolicy: UpdatePolicy.IMMEDIATE,
  autoDownload: true,
  autoInstall: true,
  minimumBackgroundDuration: 0,
  enableAutoCheck: true,
  enableAnalytics: true,
  updateCheckInterval: 15 * 60 * 1000, // 15 minutes
};

/**
 * HYBRID CONFIGURATION
 * - Automatic for optional updates
 * - Immediate for mandatory updates
 * - Best of both worlds
 */
export const HYBRID_CONFIG = {
  checkFrequency: 'on_app_resume' as const,
  updatePolicy: UpdatePolicy.SILENT,
  autoDownload: true,
  autoInstall: true, // Will be overridden based on update type
  minimumBackgroundDuration: 10000,
  enableAutoCheck: true,
  enableAnalytics: true,
  updateCheckInterval: 30 * 60 * 1000,
  // Special handling for different update types
  mandatoryPolicy: UpdatePolicy.IMMEDIATE,
  optionalPolicy: UpdatePolicy.SILENT,
};

// ============================================================================
// ENVIRONMENT-SPECIFIC CONFIGURATIONS
// ============================================================================

/**
 * Development Configuration
 * - More frequent checks for testing
 * - Manual updates for better control during development
 */
export const DEVELOPMENT_CONFIG = {
  ...MANUAL_CONFIG,
  updateCheckInterval: 5 * 60 * 1000, // 5 minutes
  enableAnalytics: true, // Enable for testing
};

/**
 * Production Configuration
 * - Optimized for end users
 * - Automatic updates for better user experience
 */
export const PRODUCTION_CONFIG = {
  ...AUTOMATIC_SILENT_CONFIG,
  updateCheckInterval: 60 * 60 * 1000, // 1 hour
  enableAnalytics: true,
};

/**
 * Beta Configuration
 * - For beta testers
 * - More frequent updates
 * - Manual control for feedback
 */
export const BETA_CONFIG = {
  ...SMART_AUTOMATIC_CONFIG,
  updateCheckInterval: 15 * 60 * 1000, // 15 minutes
  enableAnalytics: true,
};

// ============================================================================
// ACTIVE CONFIGURATION
// ============================================================================

/**
 * Current Active Configuration
 * 
 * Change this to switch between different update policies:
 * 
 * For completely automatic updates:
 * export const OTA_CONFIG = AUTOMATIC_SILENT_CONFIG;
 * 
 * For smart automatic updates:
 * export const OTA_CONFIG = SMART_AUTOMATIC_CONFIG;
 * 
 * For manual updates:
 * export const OTA_CONFIG = MANUAL_CONFIG;
 * 
 * For immediate updates:
 * export const OTA_CONFIG = IMMEDIATE_CONFIG;
 * 
 * For environment-based:
 * export const OTA_CONFIG = __DEV__ ? DEVELOPMENT_CONFIG : PRODUCTION_CONFIG;
 */

// 🚀 CHANGE THIS LINE TO SWITCH UPDATE POLICIES:
export const OTA_CONFIG = AUTOMATIC_SILENT_CONFIG; // ← FULLY AUTOMATIC!

// Alternative configurations you can use:
// export const OTA_CONFIG = SMART_AUTOMATIC_CONFIG;  // Smart automatic
// export const OTA_CONFIG = MANUAL_CONFIG;           // Manual updates
// export const OTA_CONFIG = IMMEDIATE_CONFIG;        // Immediate updates
// export const OTA_CONFIG = __DEV__ ? DEVELOPMENT_CONFIG : PRODUCTION_CONFIG; // Environment-based

// ============================================================================
// CONFIGURATION HELPERS
// ============================================================================

/**
 * Get configuration based on update type
 */
export const getConfigForUpdate = (isMandatory: boolean) => {
  if (isMandatory) {
    // Always use immediate for mandatory updates
    return {
      ...OTA_CONFIG,
      updatePolicy: UpdatePolicy.IMMEDIATE,
      autoDownload: true,
      autoInstall: true,
    };
  }
  
  return OTA_CONFIG;
};

/**
 * Get user-friendly description of current policy
 */
export const getPolicyDescription = () => {
  switch (OTA_CONFIG.updatePolicy) {
    case UpdatePolicy.SILENT:
      return 'Updates install automatically in the background';
    case UpdatePolicy.ON_APP_RESUME:
      return 'Updates install when you reopen the app';
    case UpdatePolicy.MANUAL:
      return 'You choose when to install updates';
    case UpdatePolicy.IMMEDIATE:
      return 'Updates install immediately when available';
    default:
      return 'Custom update policy';
  }
};

/**
 * Check if current configuration is automatic
 */
export const isAutomaticUpdates = () => {
  return OTA_CONFIG.autoDownload && OTA_CONFIG.autoInstall;
};

// ============================================================================
// RUNTIME CONFIGURATION UPDATES
// ============================================================================

/**
 * Update configuration at runtime
 * Useful for A/B testing or user preferences
 */
export const updateOTAConfig = (newConfig: Partial<typeof OTA_CONFIG>) => {
  Object.assign(OTA_CONFIG, newConfig);
};

/**
 * Enable/disable automatic updates at runtime
 */
export const setAutomaticUpdates = (enabled: boolean) => {
  updateOTAConfig({
    autoDownload: enabled,
    autoInstall: enabled,
    updatePolicy: enabled ? UpdatePolicy.SILENT : UpdatePolicy.MANUAL,
  });
};

/**
 * Set update frequency
 */
export const setUpdateFrequency = (minutes: number) => {
  updateOTAConfig({
    updateCheckInterval: minutes * 60 * 1000,
  });
};
