/**
 * International Airtime API Routes
 * 
 * Ultra-secure API routes for international airtime/data/pin purchases with enhanced
 * security, validation, rate limiting, and comprehensive monitoring for cross-border transactions.
 * 
 * Routes:
 * - GET /api/v1/international-airtime/countries - Get available countries
 * - GET /api/v1/international-airtime/product-types - Get product types for country
 * - GET /api/v1/international-airtime/operators - Get operators for country/product
 * - GET /api/v1/international-airtime/variations - Get variations for operator/product
 * - POST /api/v1/international-airtime/purchase - Purchase international airtime/data/pin
 * - GET /api/v1/international-airtime/history - Get international transaction history
 * 
 * Enhanced Security Features:
 * - Enhanced JWT authentication with verification requirements
 * - Stricter rate limiting for international transactions
 * - Geographic validation and compliance
 * - Enhanced fraud detection
 * - Comprehensive audit logging
 * - Multi-layer input validation
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const express = require('express');
const rateLimit = require('express-rate-limit');
const { query, validationResult } = require('express-validator');

// Import controllers and middleware
const { 
  getCountries,
  getProductTypes,
  getOperators,
  getVariations,
  purchaseInternationalAirtime,
  getInternationalHistory,
  getValidationRules
} = require('../controllers/internationalAirtimeController');

const authService = require('../services/authService');
const { 
  validateInternationalLimits, 
  detectInternationalFraud, 
  createInternationalRateLimit 
} = require('../middleware/internationalSecurity');

const logger = require('../utils/logger');

const router = express.Router();

// =====================================================
// ENHANCED MIDDLEWARE SETUP
// =====================================================

/**
 * Enhanced authentication middleware - All routes require valid JWT
 */
router.use(authService.protect);

/**
 * Enhanced user verification check for international transactions
 */
router.use(async (req, res, next) => {
  try {
    if (!req.user || !req.user.is_active) {
      logger.warn('🚫 [INTERNATIONAL_AIRTIME_ROUTES] Inactive user attempted international access:', {
        userId: req.user?.id,
        ip: req.ip
      });
      
      return res.status(403).json({
        success: false,
        message: 'Account is not active',
        code: 'ACCOUNT_INACTIVE'
      });
    }

    // Enhanced verification requirements for international transactions
    if (req.method === 'POST' && req.path === '/purchase') {
      if (!req.user.is_email_verified || !req.user.is_phone_verified) {
        logger.warn('🚫 [INTERNATIONAL_AIRTIME_ROUTES] Insufficient verification for international transaction:', {
          userId: req.user.id,
          emailVerified: req.user.is_email_verified,
          phoneVerified: req.user.is_phone_verified,
          ip: req.ip
        });
        
        return res.status(403).json({
          success: false,
          message: 'Email and phone verification required for international transactions',
          code: 'INSUFFICIENT_VERIFICATION_INTERNATIONAL'
        });
      }
    }

    next();
  } catch (error) {
    logger.error('❌ [INTERNATIONAL_AIRTIME_ROUTES] User check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
});

/**
 * Ultra-strict rate limiting for international purchases
 */
const internationalPurchaseRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: (req) => {
    // Much stricter limits for international transactions
    const baseLimit = 5; // Only 5 international transactions per hour
    
    // Reduce further for unverified users
    if (!req.user?.is_bvn_verified) {
      return Math.floor(baseLimit / 2);
    }
    
    return baseLimit;
  },
  keyGenerator: (req) => `international_purchase:${req.user.id}:${req.ip}`,
  message: {
    success: false,
    message: 'Too many international transaction attempts. Please try again later.',
    code: 'INTERNATIONAL_PURCHASE_RATE_LIMIT_EXCEEDED',
    retryAfter: 3600
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('🚫 [INTERNATIONAL_AIRTIME_ROUTES] International purchase rate limit exceeded:', {
      userId: req.user?.id,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.status(429).json({
      success: false,
      message: 'Too many international transaction attempts. Please try again later.',
      code: 'INTERNATIONAL_PURCHASE_RATE_LIMIT_EXCEEDED',
      retryAfter: 3600
    });
  }
});

/**
 * General rate limiting for international operations
 */
const internationalGeneralRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // 50 requests per 15 minutes (stricter than domestic)
  keyGenerator: (req) => `international_general:${req.user.id}:${req.ip}`,
  message: {
    success: false,
    message: 'Too many international requests. Please try again later.',
    code: 'INTERNATIONAL_GENERAL_RATE_LIMIT_EXCEEDED'
  }
});

// =====================================================
// ROUTE DEFINITIONS
// =====================================================

/**
 * @route   GET /api/v1/international-airtime/countries
 * @desc    Get all available countries for international airtime
 * @access  Private (Verified users only)
 */
router.get('/countries',
  internationalGeneralRateLimit,
  async (req, res) => {
    const startTime = Date.now();
    
    try {
      logger.info('🌍 [INTERNATIONAL_AIRTIME_ROUTES] Countries request received:', {
        userId: req.user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      await getCountries(req, res);
      
      const duration = Date.now() - startTime;
      logger.info('✅ [INTERNATIONAL_AIRTIME_ROUTES] Countries request completed:', {
        userId: req.user.id,
        duration: `${duration}ms`,
        status: res.statusCode
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('❌ [INTERNATIONAL_AIRTIME_ROUTES] Countries request failed:', {
        userId: req.user.id,
        duration: `${duration}ms`,
        error: error.message
      });
      
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Failed to retrieve countries',
          code: 'COUNTRIES_ERROR'
        });
      }
    }
  }
);

/**
 * @route   GET /api/v1/international-airtime/product-types
 * @desc    Get product types for a specific country
 * @access  Private
 */
router.get('/product-types',
  internationalGeneralRateLimit,
  [
    query('country_code')
      .notEmpty()
      .withMessage('Country code is required')
      .isLength({ min: 2, max: 2 })
      .withMessage('Country code must be exactly 2 characters')
      .matches(/^[A-Z]{2}$/)
      .withMessage('Country code must be uppercase letters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      logger.info('🌍 [INTERNATIONAL_AIRTIME_ROUTES] Product types request:', {
        userId: req.user.id,
        countryCode: req.query.country_code
      });

      await getProductTypes(req, res);
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_ROUTES] Product types request failed:', {
        userId: req.user.id,
        countryCode: req.query.country_code,
        error: error.message
      });
      
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Failed to retrieve product types'
        });
      }
    }
  }
);

/**
 * @route   GET /api/v1/international-airtime/operators
 * @desc    Get operators for a specific country and product type
 * @access  Private
 */
router.get('/operators',
  internationalGeneralRateLimit,
  [
    query('country_code')
      .notEmpty()
      .withMessage('Country code is required')
      .matches(/^[A-Z]{2}$/)
      .withMessage('Country code must be uppercase letters'),
    query('product_type_id')
      .notEmpty()
      .withMessage('Product type ID is required')
      .isNumeric()
      .withMessage('Product type ID must be numeric')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      logger.info('🌍 [INTERNATIONAL_AIRTIME_ROUTES] Operators request:', {
        userId: req.user.id,
        countryCode: req.query.country_code,
        productTypeId: req.query.product_type_id
      });

      await getOperators(req, res);
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_ROUTES] Operators request failed:', {
        userId: req.user.id,
        error: error.message
      });
      
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Failed to retrieve operators'
        });
      }
    }
  }
);

/**
 * @route   GET /api/v1/international-airtime/variations
 * @desc    Get variations for a specific operator and product type
 * @access  Private
 */
router.get('/variations',
  internationalGeneralRateLimit,
  [
    query('operator_id')
      .notEmpty()
      .withMessage('Operator ID is required')
      .isNumeric()
      .withMessage('Operator ID must be numeric'),
    query('product_type_id')
      .notEmpty()
      .withMessage('Product type ID is required')
      .isNumeric()
      .withMessage('Product type ID must be numeric')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      logger.info('🌍 [INTERNATIONAL_AIRTIME_ROUTES] Variations request:', {
        userId: req.user.id,
        operatorId: req.query.operator_id,
        productTypeId: req.query.product_type_id
      });

      await getVariations(req, res);
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_ROUTES] Variations request failed:', {
        userId: req.user.id,
        error: error.message
      });
      
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Failed to retrieve variations'
        });
      }
    }
  }
);

/**
 * @route   POST /api/v1/international-airtime/purchase
 * @desc    Purchase international airtime/data/pin with enhanced security
 * @access  Private (Enhanced verification required)
 * @security Ultra-strict rate limiting, fraud detection, geographic validation
 */
router.post('/purchase',
  internationalPurchaseRateLimit,
  getValidationRules(),
  validateInternationalLimits,
  detectInternationalFraud,
  async (req, res) => {
    const startTime = Date.now();

    try {
      logger.info('🌍 [INTERNATIONAL_AIRTIME_ROUTES] International purchase request received:', {
        userId: req.user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        country: req.body.country_code,
        operator: req.body.operator_id,
        amount: req.body.amount,
        recipient: req.body.billersCode?.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3'),
        internationalRiskScore: req.internationalRiskScore
      });

      await purchaseInternationalAirtime(req, res);

      const duration = Date.now() - startTime;
      logger.info('✅ [INTERNATIONAL_AIRTIME_ROUTES] International purchase request completed:', {
        userId: req.user.id,
        duration: `${duration}ms`,
        status: res.statusCode,
        country: req.body.country_code
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('❌ [INTERNATIONAL_AIRTIME_ROUTES] International purchase request failed:', {
        userId: req.user.id,
        duration: `${duration}ms`,
        country: req.body.country_code,
        error: error.message
      });

      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'International purchase request failed',
          code: 'INTERNATIONAL_PURCHASE_ERROR'
        });
      }
    }
  }
);

/**
 * @route   GET /api/v1/international-airtime/history
 * @desc    Get user's international transaction history
 * @access  Private
 */
router.get('/history',
  internationalGeneralRateLimit,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50'),
    query('status')
      .optional()
      .isIn(['pending', 'completed', 'failed', 'cancelled'])
      .withMessage('Invalid status value'),
    query('country_code')
      .optional()
      .matches(/^[A-Z]{2}$/)
      .withMessage('Country code must be uppercase letters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      logger.info('📋 [INTERNATIONAL_AIRTIME_ROUTES] International history request:', {
        userId: req.user.id,
        page: req.query.page,
        limit: req.query.limit,
        status: req.query.status,
        countryCode: req.query.country_code
      });

      await getInternationalHistory(req, res);
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_ROUTES] International history request failed:', {
        userId: req.user.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        message: 'Failed to retrieve international transaction history'
      });
    }
  }
);

// =====================================================
// ERROR HANDLING
// =====================================================

/**
 * Route-specific error handler
 */
router.use((error, req, res, next) => {
  logger.error('❌ [INTERNATIONAL_AIRTIME_ROUTES] Unhandled route error:', {
    error: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    userId: req.user?.id,
    ip: req.ip,
    country: req.body?.country_code || req.query?.country_code
  });

  if (!res.headersSent) {
    res.status(500).json({
      success: false,
      message: 'An unexpected error occurred in international service',
      code: 'INTERNATIONAL_INTERNAL_ERROR'
    });
  }
});

module.exports = router;
