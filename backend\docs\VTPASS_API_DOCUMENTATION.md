# VTpass MTN VTU API Integration Documentation

## Overview

This document provides comprehensive documentation for the VTpass MTN VTU API integration implemented in the PayVendy backend system. The integration includes enterprise-grade security, automatic failover, comprehensive error handling, and real-time transaction monitoring.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Security Features](#security-features)
3. [API Endpoints](#api-endpoints)
4. [Authentication](#authentication)
5. [Error Handling](#error-handling)
6. [Provider Management](#provider-management)
7. [Configuration](#configuration)
8. [Monitoring & Analytics](#monitoring--analytics)
9. [Testing](#testing)
10. [Deployment](#deployment)

## Architecture Overview

### System Components

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client App    │───▶│   API Gateway    │───▶│  Auth Service   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Payment Security│◀───│ Airtime Controller│───▶│ VTU Provider    │
│   Middleware    │    │                  │    │    Manager      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Database      │◀───│ Transaction      │───▶│  VTpass Service │
│   (Supabase)    │    │   Controller     │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Key Features

- **Multi-Provider Support**: Primary VTpass integration with backup provider architecture
- **Automatic Failover**: Circuit breaker pattern with intelligent provider routing
- **Enterprise Security**: Multi-layer security with fraud detection and rate limiting
- **Real-time Monitoring**: Comprehensive transaction tracking and analytics
- **Atomic Operations**: Database transactions ensure data consistency

## Security Features

### 1. Authentication & Authorization
- JWT-based authentication required for all endpoints
- User ownership validation for transaction queries
- Role-based access control integration

### 2. Payment Security Middleware
- Request signature validation
- IP address monitoring and geolocation checking
- Progressive rate limiting with user-specific limits
- Fraud detection with risk scoring
- Transaction amount validation and daily limits

### 3. Input Validation
- Comprehensive input sanitization
- Phone number format validation for MTN network
- Amount range validation with configurable limits
- SQL injection and XSS prevention

### 4. Data Protection
- Sensitive data masking in logs
- Encrypted PIN storage and verification
- Secure API key management
- HTTPS-only communication

## API Endpoints

### Airtime Purchase

**POST** `/api/v1/airtime/purchase`

Purchase MTN airtime with comprehensive security and validation.

#### Request Headers
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Request-ID: <unique_request_id>
```

#### Request Body
```json
{
  "phone": "08031234567",
  "amount": 1000,
  "pin": "1234",
  "saveRecipient": true,
  "recipientName": "John Doe"
}
```

#### Response (Success)
```json
{
  "success": true,
  "message": "Transaction successful",
  "data": {
    "transactionId": "uuid-here",
    "status": "completed",
    "amount": 1000,
    "recipient": "08031234567",
    "provider": "MTN",
    "requestId": "req_123456789",
    "timestamp": "2025-07-11T10:30:00.000Z"
  }
}
```

#### Response (Error)
```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_BALANCE",
    "category": "insufficient_funds",
    "message": "Insufficient balance. Please fund your wallet and try again.",
    "retryable": false,
    "severity": "warning",
    "timestamp": "2025-07-11T10:30:00.000Z",
    "requestId": "req_123456789"
  }
}
```

### Transaction Status Query

**GET** `/api/v1/transactions/:transactionId/status`

Query transaction status with real-time updates from VTpass.

#### Response
```json
{
  "success": true,
  "data": {
    "transaction": {
      "id": "uuid-here",
      "reference": "2025071110300012345678",
      "type": "airtime",
      "amount": 1000,
      "recipient": "08031234567",
      "provider": "MTN",
      "status": "completed",
      "description": "MTN Airtime ₦1000 to 08031234567",
      "providerTransactionId": "vtpass_tx_123456",
      "createdAt": "2025-07-11T10:30:00.000Z",
      "updatedAt": "2025-07-11T10:31:00.000Z",
      "completedAt": "2025-07-11T10:31:00.000Z"
    },
    "lastUpdated": "2025-07-11T10:35:00.000Z",
    "requestId": "status_123456789"
  }
}
```

### Transaction History

**GET** `/api/v1/transactions`

Get paginated transaction history with filtering options.

#### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `status` (optional): Filter by status (pending, completed, failed, cancelled)
- `type` (optional): Filter by type (airtime, data, electricity, cable)
- `provider` (optional): Filter by provider
- `startDate` (optional): Start date (ISO 8601)
- `endDate` (optional): End date (ISO 8601)

#### Response
```json
{
  "success": true,
  "data": {
    "transactions": [...],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "pages": 8
    }
  }
}
```

### Provider Information

**GET** `/api/v1/airtime/providers`

Get available airtime providers and their capabilities.

#### Response
```json
{
  "success": true,
  "data": {
    "providers": [
      {
        "id": "mtn",
        "name": "MTN Nigeria",
        "code": "MTN",
        "minAmount": 50,
        "maxAmount": 50000,
        "available": true,
        "processingTime": "1-5 minutes",
        "supportedPrefixes": ["0803", "0806", "0813", "0816", "0903", "0906", "0913", "0916"],
        "features": [
          "Instant delivery",
          "Real-time confirmation",
          "24/7 availability",
          "Secure transactions"
        ]
      }
    ],
    "totalProviders": 1,
    "lastUpdated": "2025-07-11T10:30:00.000Z"
  }
}
```

### User Limits

**GET** `/api/v1/airtime/limits`

Get user's transaction limits and restrictions.

#### Response
```json
{
  "success": true,
  "data": {
    "transaction": {
      "min": 50,
      "max": 50000,
      "currency": "NGN"
    },
    "daily": {
      "amount": 1000000,
      "transactions": 50,
      "currency": "NGN"
    },
    "hourly": {
      "transactions": 20
    },
    "verification": {
      "emailVerified": true,
      "phoneVerified": true,
      "benefits": []
    },
    "restrictions": {
      "requiresPinForLargeAmounts": true,
      "pinThreshold": 10000,
      "maintenanceMode": false
    }
  }
}
```

## Error Handling

### Error Categories

1. **Authentication Errors** (`authentication`)
   - Invalid credentials
   - Expired tokens
   - Insufficient permissions

2. **Validation Errors** (`validation`)
   - Invalid input format
   - Missing required fields
   - Out of range values

3. **Insufficient Funds** (`insufficient_funds`)
   - Low wallet balance
   - Exceeded transaction limits

4. **Service Unavailable** (`service_unavailable`)
   - Provider maintenance
   - Network issues
   - Temporary outages

5. **Rate Limiting** (`rate_limit`)
   - Too many requests
   - Exceeded hourly/daily limits

6. **System Errors** (`system`)
   - Database errors
   - Internal server errors

### Error Response Format

All errors follow a consistent format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "category": "error_category",
    "message": "User-friendly error message",
    "retryable": true/false,
    "retryDelay": 30000,
    "severity": "error|warning|info",
    "action": "suggested_action",
    "timestamp": "2025-07-11T10:30:00.000Z",
    "requestId": "req_123456789"
  }
}
```

### Retry Logic

The system implements intelligent retry logic:

- **Retryable Errors**: Network issues, timeouts, temporary service unavailability
- **Non-Retryable Errors**: Authentication failures, validation errors, insufficient funds
- **Exponential Backoff**: Increasing delays between retries (1s, 2s, 4s, etc.)
- **Maximum Retries**: 3 attempts per provider
- **Circuit Breaker**: Automatic provider failover on repeated failures

## Provider Management

### Circuit Breaker Pattern

The system implements circuit breaker pattern for provider reliability:

- **Closed State**: Normal operation, requests pass through
- **Open State**: Provider failing, requests blocked for recovery period
- **Half-Open State**: Testing if provider has recovered

### Provider Selection Algorithm

1. **Priority-based**: Primary providers tried first
2. **Health-based**: Exclude providers with open circuit breakers
3. **Performance-based**: Consider success rate and response time
4. **Load balancing**: Distribute load across available providers

### Failover Strategy

```
Primary Provider (VTpass) → Backup Provider → Error Response
```

- Automatic failover on provider failures
- Intelligent error categorization
- Provider health monitoring
- Real-time status updates

## Configuration

### Environment Variables

```bash
# VTpass Configuration
VTPASS_API_KEY=your-vtpass-api-key
VTPASS_SECRET_KEY=your-vtpass-secret-key
VTPASS_PUBLIC_KEY=your-vtpass-public-key
VTPASS_BASE_URL=https://vtpass.com/api
VTPASS_TIMEOUT=30000
VTPASS_MAX_RETRIES=3
VTPASS_MIN_AMOUNT=50
VTPASS_MAX_AMOUNT=50000

# Security Configuration
MAX_DAILY_PAYMENT_AMOUNT=1000000
MAX_TRANSACTION_AMOUNT=100000
MAX_TRANSACTIONS_PER_HOUR=20
MAX_TRANSACTIONS_PER_DAY=50
PAYMENT_SIGNATURE_SECRET=your-signature-secret

# Provider Management
BACKUP_VTU_PROVIDER=none
BACKUP_VTU_API_KEY=your-backup-api-key
```

### Database Schema

The integration extends the existing transaction schema:

```sql
-- Additional columns for VTpass integration
ALTER TABLE transactions ADD COLUMN provider_type VARCHAR(50) DEFAULT 'vtpass';
ALTER TABLE transactions ADD COLUMN provider_transaction_id VARCHAR(100);
ALTER TABLE transactions ADD COLUMN commission_amount DECIMAL(15,4) DEFAULT 0.0000;
ALTER TABLE transactions ADD COLUMN total_amount DECIMAL(15,2);
ALTER TABLE transactions ADD COLUMN retry_count INTEGER DEFAULT 0;
ALTER TABLE transactions ADD COLUMN completed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE transactions ADD COLUMN failure_reason VARCHAR(100);
ALTER TABLE transactions ADD COLUMN ip_address INET;
```

## Monitoring & Analytics

### Transaction Metrics

- Success rate by provider
- Average response time
- Transaction volume trends
- Error rate analysis
- Provider performance comparison

### Security Monitoring

- Failed authentication attempts
- Suspicious transaction patterns
- Rate limit violations
- Fraud detection alerts
- IP address monitoring

### Health Checks

- Provider availability monitoring
- Circuit breaker status
- Database connectivity
- API response times
- System resource usage

## Testing

### Test Environment Setup

1. **Sandbox Configuration**
   ```bash
   VTPASS_BASE_URL=https://sandbox.vtpass.com/api
   MOCK_VTPASS=true
   ```

2. **Test Phone Numbers** (Sandbox)
   - `08011111111`: Successful transaction
   - `201000000000`: Pending response
   - `500000000000`: Unexpected response
   - `400000000000`: No response
   - `300000000000`: Timeout
   - Any other number: Failed transaction

### Unit Tests

```bash
# Run VTpass service tests
npm test -- --grep "VTpass"

# Run provider manager tests
npm test -- --grep "Provider Manager"

# Run security middleware tests
npm test -- --grep "Payment Security"
```

### Integration Tests

```bash
# Run full integration test suite
npm run test:integration

# Test specific scenarios
npm run test:integration -- --grep "Airtime Purchase"
```

## Deployment

### Production Checklist

- [ ] Configure production VTpass API credentials
- [ ] Set up SSL certificates for HTTPS
- [ ] Configure rate limiting and security settings
- [ ] Set up monitoring and alerting
- [ ] Run database migrations
- [ ] Test provider failover scenarios
- [ ] Configure backup provider (when available)
- [ ] Set up log aggregation
- [ ] Configure error tracking (Sentry)
- [ ] Test security measures

### Monitoring Setup

1. **Application Monitoring**
   - Error tracking with Sentry
   - Performance monitoring
   - Custom metrics dashboard

2. **Infrastructure Monitoring**
   - Server health checks
   - Database performance
   - Network connectivity
   - SSL certificate expiry

3. **Business Metrics**
   - Transaction success rates
   - Revenue tracking
   - User activity patterns
   - Provider performance

### Scaling Considerations

- **Horizontal Scaling**: Load balancer configuration
- **Database Optimization**: Connection pooling and indexing
- **Caching Strategy**: Redis for frequently accessed data
- **Rate Limiting**: Distributed rate limiting for multiple instances
- **Provider Load Balancing**: Intelligent request distribution

---

## Support & Maintenance

For technical support or questions about this integration:

- **Development Team**: PayVendy Backend Team
- **Documentation**: This file and inline code comments
- **Issue Tracking**: GitHub Issues
- **Monitoring**: Application dashboard and alerts

**Last Updated**: July 11, 2025
**Version**: 1.0.0
