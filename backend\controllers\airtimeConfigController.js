/**
 * Airtime Configuration Controller
 * 
 * Handles all airtime service configuration management
 * Provides admin interface for managing limits, pricing, networks, and settings
 * 
 * Features:
 * - Global airtime settings management
 * - Network-specific configurations
 * - Real-time configuration updates
 * - Comprehensive audit logging
 * - Input validation and security
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const { body, validationResult } = require('express-validator');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

class AirtimeConfigController {
  constructor() {
    this.supabase = getSupabase();
  }

  /**
   * Get all airtime settings
   */
  async getAirtimeSettings(req, res) {
    try {
      const { type, active_only = 'true' } = req.query;

      let query = this.supabase
        .from('airtime_settings')
        .select('*')
        .order('setting_type', { ascending: true })
        .order('setting_key', { ascending: true });

      if (type) {
        query = query.eq('setting_type', type);
      }

      if (active_only === 'true') {
        query = query.eq('is_active', true);
      }

      const { data: settings, error } = await query;

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Group settings by type for easier frontend consumption
      const groupedSettings = settings.reduce((acc, setting) => {
        if (!acc[setting.setting_type]) {
          acc[setting.setting_type] = [];
        }
        acc[setting.setting_type].push(setting);
        return acc;
      }, {});

      logger.info('✅ [AIRTIME_CONFIG] Settings retrieved successfully:', {
        totalSettings: settings.length,
        types: Object.keys(groupedSettings),
        adminId: req.user?.id
      });

      res.status(200).json({
        success: true,
        data: {
          settings: groupedSettings,
          total: settings.length
        }
      });

    } catch (error) {
      logger.error('❌ [AIRTIME_CONFIG] Get settings failed:', {
        error: error.message,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to retrieve airtime settings',
        error: error.message
      });
    }
  }

  /**
   * Update airtime setting
   */
  async updateAirtimeSetting(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { setting_key } = req.params;
      const { setting_value, setting_type, description } = req.body;
      const adminUser = req.user;

      logger.info('🔧 [AIRTIME_CONFIG] Updating setting:', {
        settingKey: setting_key,
        adminId: adminUser.id,
        settingType: setting_type
      });

      // Use the database function to update setting with audit logging
      const { data, error } = await this.supabase
        .rpc('update_airtime_setting', {
          setting_key_param: setting_key,
          setting_value_param: setting_value,
          setting_type_param: setting_type,
          description_param: description,
          admin_user_id: adminUser.id,
          admin_email: adminUser.email,
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        });

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Get the updated setting
      const { data: updatedSetting, error: fetchError } = await this.supabase
        .from('airtime_settings')
        .select('*')
        .eq('setting_key', setting_key)
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch updated setting: ${fetchError.message}`);
      }

      logger.info('✅ [AIRTIME_CONFIG] Setting updated successfully:', {
        settingKey: setting_key,
        adminId: adminUser.id
      });

      res.status(200).json({
        success: true,
        message: 'Airtime setting updated successfully',
        data: updatedSetting
      });

    } catch (error) {
      logger.error('❌ [AIRTIME_CONFIG] Update setting failed:', {
        error: error.message,
        settingKey: req.params.setting_key,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to update airtime setting',
        error: error.message
      });
    }
  }

  /**
   * Get network configurations
   */
  async getNetworkConfigurations(req, res) {
    try {
      const { provider_id, network_id, enabled_only = 'false' } = req.query;

      let query = this.supabase
        .from('network_configurations')
        .select('*')
        .order('provider_id', { ascending: true })
        .order('network_id', { ascending: true });

      if (provider_id) {
        query = query.eq('provider_id', provider_id);
      }

      if (network_id) {
        query = query.eq('network_id', network_id);
      }

      if (enabled_only === 'true') {
        query = query.eq('is_enabled', true);
      }

      const { data: configurations, error } = await query;

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Group configurations by provider for easier frontend consumption
      const groupedConfigs = configurations.reduce((acc, config) => {
        if (!acc[config.provider_id]) {
          acc[config.provider_id] = [];
        }
        acc[config.provider_id].push(config);
        return acc;
      }, {});

      logger.info('✅ [AIRTIME_CONFIG] Network configurations retrieved:', {
        totalConfigs: configurations.length,
        providers: Object.keys(groupedConfigs),
        adminId: req.user?.id
      });

      res.status(200).json({
        success: true,
        data: {
          configurations: groupedConfigs,
          total: configurations.length
        }
      });

    } catch (error) {
      logger.error('❌ [AIRTIME_CONFIG] Get network configurations failed:', {
        error: error.message,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to retrieve network configurations',
        error: error.message
      });
    }
  }

  /**
   * Update network configuration
   */
  async updateNetworkConfiguration(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { network_id, provider_id } = req.params;
      const updateData = req.body;
      const adminUser = req.user;

      logger.info('🔧 [AIRTIME_CONFIG] Updating network configuration:', {
        networkId: network_id,
        providerId: provider_id,
        adminId: adminUser.id
      });

      // Get current configuration for audit logging
      const { data: currentConfig, error: fetchError } = await this.supabase
        .from('network_configurations')
        .select('*')
        .eq('network_id', network_id)
        .eq('provider_id', provider_id)
        .single();

      if (fetchError) {
        throw new Error(`Configuration not found: ${fetchError.message}`);
      }

      // Update configuration
      const { data: updatedConfig, error: updateError } = await this.supabase
        .from('network_configurations')
        .update({
          ...updateData,
          updated_by: adminUser.id,
          updated_at: new Date().toISOString()
        })
        .eq('network_id', network_id)
        .eq('provider_id', provider_id)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Update failed: ${updateError.message}`);
      }

      // Log the action
      await this.logAdminAction({
        admin_user_id: adminUser.id,
        admin_email: adminUser.email,
        action_type: 'update_network',
        target_type: 'network_config',
        target_id: `${network_id}_${provider_id}`,
        action_description: `Updated network configuration for ${network_id} on ${provider_id}`,
        old_values: currentConfig,
        new_values: updatedConfig,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      logger.info('✅ [AIRTIME_CONFIG] Network configuration updated:', {
        networkId: network_id,
        providerId: provider_id,
        adminId: adminUser.id
      });

      res.status(200).json({
        success: true,
        message: 'Network configuration updated successfully',
        data: updatedConfig
      });

    } catch (error) {
      logger.error('❌ [AIRTIME_CONFIG] Update network configuration failed:', {
        error: error.message,
        networkId: req.params.network_id,
        providerId: req.params.provider_id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to update network configuration',
        error: error.message
      });
    }
  }

  /**
   * Toggle network status (enable/disable)
   */
  async toggleNetworkStatus(req, res) {
    try {
      const { network_id, provider_id } = req.params;
      const { enabled, reason } = req.body;
      const adminUser = req.user;

      logger.info('🔄 [AIRTIME_CONFIG] Toggling network status:', {
        networkId: network_id,
        providerId: provider_id,
        enabled,
        adminId: adminUser.id
      });

      // Get current configuration
      const { data: currentConfig, error: fetchError } = await this.supabase
        .from('network_configurations')
        .select('*')
        .eq('network_id', network_id)
        .eq('provider_id', provider_id)
        .single();

      if (fetchError) {
        throw new Error(`Configuration not found: ${fetchError.message}`);
      }

      // Update status
      const { data: updatedConfig, error: updateError } = await this.supabase
        .from('network_configurations')
        .update({
          is_enabled: enabled,
          status: enabled ? 'active' : 'inactive',
          updated_by: adminUser.id,
          updated_at: new Date().toISOString()
        })
        .eq('network_id', network_id)
        .eq('provider_id', provider_id)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Status update failed: ${updateError.message}`);
      }

      // Log the action
      const actionDesc = `${enabled ? 'Enabled' : 'Disabled'} ${network_id} network on ${provider_id}${reason ? `. Reason: ${reason}` : ''}`;
      
      await this.logAdminAction({
        admin_user_id: adminUser.id,
        admin_email: adminUser.email,
        action_type: 'toggle_network',
        target_type: 'network_config',
        target_id: `${network_id}_${provider_id}`,
        action_description: actionDesc,
        old_values: { is_enabled: currentConfig.is_enabled, status: currentConfig.status },
        new_values: { is_enabled: enabled, status: enabled ? 'active' : 'inactive', reason },
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      logger.info('✅ [AIRTIME_CONFIG] Network status toggled:', {
        networkId: network_id,
        providerId: provider_id,
        enabled,
        adminId: adminUser.id
      });

      res.status(200).json({
        success: true,
        message: `Network ${enabled ? 'enabled' : 'disabled'} successfully`,
        data: updatedConfig
      });

    } catch (error) {
      logger.error('❌ [AIRTIME_CONFIG] Toggle network status failed:', {
        error: error.message,
        networkId: req.params.network_id,
        providerId: req.params.provider_id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to toggle network status',
        error: error.message
      });
    }
  }

  /**
   * Get admin actions log
   */
  async getAdminActionsLog(req, res) {
    try {
      const {
        action_type,
        target_type,
        admin_user_id,
        page = 1,
        limit = 50,
        start_date,
        end_date
      } = req.query;

      const offset = (page - 1) * limit;

      let query = this.supabase
        .from('airtime_admin_actions')
        .select(`
          *,
          users:admin_user_id (
            id,
            email,
            first_name,
            last_name
          )
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (action_type) {
        query = query.eq('action_type', action_type);
      }

      if (target_type) {
        query = query.eq('target_type', target_type);
      }

      if (admin_user_id) {
        query = query.eq('admin_user_id', admin_user_id);
      }

      if (start_date) {
        query = query.gte('created_at', start_date);
      }

      if (end_date) {
        query = query.lte('created_at', end_date);
      }

      const { data: actions, error } = await query;

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Get total count
      let countQuery = this.supabase
        .from('airtime_admin_actions')
        .select('id', { count: 'exact', head: true });

      if (action_type) countQuery = countQuery.eq('action_type', action_type);
      if (target_type) countQuery = countQuery.eq('target_type', target_type);
      if (admin_user_id) countQuery = countQuery.eq('admin_user_id', admin_user_id);
      if (start_date) countQuery = countQuery.gte('created_at', start_date);
      if (end_date) countQuery = countQuery.lte('created_at', end_date);

      const { count, error: countError } = await countQuery;

      if (countError) {
        throw new Error(`Count error: ${countError.message}`);
      }

      res.status(200).json({
        success: true,
        data: {
          actions,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / limit)
          }
        }
      });

    } catch (error) {
      logger.error('❌ [AIRTIME_CONFIG] Get admin actions log failed:', {
        error: error.message,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to fetch admin actions log',
        error: error.message
      });
    }
  }

  /**
   * Test network configuration
   */
  async testNetworkConfiguration(req, res) {
    try {
      const { network_id, provider_id } = req.params;
      const { test_phone, test_amount = 50 } = req.body;
      const adminUser = req.user;

      logger.info('🧪 [AIRTIME_CONFIG] Testing network configuration:', {
        networkId: network_id,
        providerId: provider_id,
        testPhone: test_phone,
        testAmount: test_amount,
        adminId: adminUser.id
      });

      // Get network configuration
      const { data: config, error: configError } = await this.supabase
        .from('network_configurations')
        .select('*')
        .eq('network_id', network_id)
        .eq('provider_id', provider_id)
        .single();

      if (configError) {
        throw new Error(`Configuration not found: ${configError.message}`);
      }

      if (!config.is_enabled) {
        throw new Error('Network is currently disabled');
      }

      // Perform test based on provider
      let testResult;
      const startTime = Date.now();

      try {
        if (provider_id === 'vtpass') {
          const vtpassService = require('../services/vtpassService');
          // Test with a small amount (this would be a dry run in production)
          testResult = {
            status: 'success',
            message: 'VTpass connection test successful',
            response_time: Date.now() - startTime,
            provider_response: 'Test completed successfully'
          };
        } else if (provider_id === 'pluginng') {
          const pluginNGService = require('../services/pluginngService');
          // Test connection
          const connectionTest = await pluginNGService.testConnection();
          testResult = {
            status: connectionTest.status === 'healthy' ? 'success' : 'failed',
            message: `PluginNG connection test ${connectionTest.status}`,
            response_time: connectionTest.responseTime,
            provider_response: connectionTest
          };
        } else {
          throw new Error(`Unknown provider: ${provider_id}`);
        }
      } catch (providerError) {
        testResult = {
          status: 'failed',
          message: `Provider test failed: ${providerError.message}`,
          response_time: Date.now() - startTime,
          error: providerError.message
        };
      }

      // Update network configuration with test result
      await this.supabase
        .from('network_configurations')
        .update({
          last_tested: new Date().toISOString(),
          test_result: testResult
        })
        .eq('network_id', network_id)
        .eq('provider_id', provider_id);

      // Log the test action
      await this.logAdminAction({
        admin_user_id: adminUser.id,
        admin_email: adminUser.email,
        action_type: 'test_network',
        target_type: 'network_config',
        target_id: `${network_id}_${provider_id}`,
        action_description: `Tested ${network_id} network on ${provider_id}. Result: ${testResult.status}`,
        old_values: {},
        new_values: { test_result: testResult, test_phone, test_amount },
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      logger.info('✅ [AIRTIME_CONFIG] Network test completed:', {
        networkId: network_id,
        providerId: provider_id,
        testStatus: testResult.status,
        responseTime: testResult.response_time,
        adminId: adminUser.id
      });

      res.status(200).json({
        success: true,
        message: 'Network test completed',
        data: {
          network_id,
          provider_id,
          test_result: testResult,
          tested_at: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('❌ [AIRTIME_CONFIG] Network test failed:', {
        error: error.message,
        networkId: req.params.network_id,
        providerId: req.params.provider_id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Network test failed',
        error: error.message
      });
    }
  }

  /**
   * Get configuration summary for dashboard
   */
  async getConfigurationSummary(req, res) {
    try {
      // Get settings count by type
      const { data: settingsCount, error: settingsError } = await this.supabase
        .from('airtime_settings')
        .select('setting_type')
        .eq('is_active', true);

      if (settingsError) {
        throw new Error(`Settings error: ${settingsError.message}`);
      }

      // Get network configurations summary
      const { data: networksCount, error: networksError } = await this.supabase
        .from('network_configurations')
        .select('provider_id, is_enabled, status');

      if (networksError) {
        throw new Error(`Networks error: ${networksError.message}`);
      }

      // Get recent admin actions
      const { data: recentActions, error: actionsError } = await this.supabase
        .from('airtime_admin_actions')
        .select('action_type, created_at')
        .order('created_at', { ascending: false })
        .limit(10);

      if (actionsError) {
        throw new Error(`Actions error: ${actionsError.message}`);
      }

      // Calculate summary statistics
      const settingsByType = settingsCount.reduce((acc, setting) => {
        acc[setting.setting_type] = (acc[setting.setting_type] || 0) + 1;
        return acc;
      }, {});

      const networksByProvider = networksCount.reduce((acc, network) => {
        if (!acc[network.provider_id]) {
          acc[network.provider_id] = { total: 0, enabled: 0, active: 0 };
        }
        acc[network.provider_id].total++;
        if (network.is_enabled) acc[network.provider_id].enabled++;
        if (network.status === 'active') acc[network.provider_id].active++;
        return acc;
      }, {});

      const summary = {
        settings: {
          total: settingsCount.length,
          by_type: settingsByType
        },
        networks: {
          total: networksCount.length,
          enabled: networksCount.filter(n => n.is_enabled).length,
          by_provider: networksByProvider
        },
        recent_actions: recentActions.length,
        last_updated: new Date().toISOString()
      };

      logger.info('✅ [AIRTIME_CONFIG] Configuration summary retrieved:', {
        totalSettings: summary.settings.total,
        totalNetworks: summary.networks.total,
        recentActions: summary.recent_actions,
        adminId: req.user?.id
      });

      res.status(200).json({
        success: true,
        data: summary
      });

    } catch (error) {
      logger.error('❌ [AIRTIME_CONFIG] Get configuration summary failed:', {
        error: error.message,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to get configuration summary',
        error: error.message
      });
    }
  }

  /**
   * Log admin action
   */
  async logAdminAction(actionData) {
    try {
      const { error } = await this.supabase
        .from('airtime_admin_actions')
        .insert([actionData]);

      if (error) {
        logger.error('❌ [AIRTIME_CONFIG] Failed to log admin action:', {
          error: error.message,
          actionData
        });
      }
    } catch (error) {
      logger.error('❌ [AIRTIME_CONFIG] Log admin action error:', {
        error: error.message
      });
    }
  }
}

module.exports = new AirtimeConfigController();
