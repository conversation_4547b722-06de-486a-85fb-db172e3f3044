# OTA Updates System

## Overview

This document describes the custom Over-The-Air (OTA) updates system implemented for the Vendy app. Since Microsoft CodePush was retired in March 2025, we've built a custom solution that provides similar functionality using our own backend infrastructure.

## Features

- ✅ Automatic update checking
- ✅ Background download with progress tracking
- ✅ Mandatory and optional updates
- ✅ Rollback capabilities
- ✅ Update analytics and tracking
- ✅ Platform-specific updates (iOS/Android)
- ✅ Version compatibility checking
- ✅ Secure bundle verification
- ✅ Professional UI with progress indicators

## Architecture

### Components

1. **OTA Service** (`src/services/codePushService.ts`)
   - Handles update checking, downloading, and installation
   - Manages update policies and configurations
   - Integrates with app state management

2. **Backend API** (`backend/routes/appUpdates.js`)
   - Provides update check and download endpoints
   - Manages update configurations and analytics
   - Handles bundle distribution

3. **Update Modal** (`src/components/UpdateModal.tsx`)
   - User-friendly update notifications
   - Progress indicators for downloads and installations
   - Handles user interactions

4. **State Management** (Zustand store integration)
   - Tracks update status and progress
   - Provides reactive UI updates

## Setup Instructions

### 1. Install Dependencies

```bash
# Run the installation script
chmod +x install-ota-dependencies.sh
./install-ota-dependencies.sh

# Or install manually
npm install react-native-fs@^2.20.0 react-native-zip-archive@^7.0.1
```

### 2. Configure Backend

1. Update the API base URL in `src/services/codePushService.ts`:
   ```typescript
   const API_BASE_URL = 'https://your-backend-url.com';
   ```

2. Create the bundles directory:
   ```bash
   mkdir -p backend/storage/app-bundles
   ```

3. Configure updates in `backend/config/app-updates.json`

### 3. Build and Deploy

For iOS:
```bash
cd ios && pod install && cd ..
npx react-native run-ios
```

For Android:
```bash
npx react-native run-android
```

## Creating Update Bundles

### 1. Build the Bundle

```bash
# For Android
npx react-native bundle \
  --platform android \
  --dev false \
  --entry-file index.js \
  --bundle-output android-bundle.js \
  --assets-dest android-assets

# For iOS
npx react-native bundle \
  --platform ios \
  --dev false \
  --entry-file index.js \
  --bundle-output ios-bundle.js \
  --assets-dest ios-assets
```

### 2. Create Update Package

```bash
# Create a zip file containing the bundle and assets
zip -r vendy-v1.0.1-android.zip android-bundle.js android-assets/

# Calculate checksum
shasum -a 256 vendy-v1.0.1-android.zip
```

### 3. Update Configuration

Add the new update to `backend/config/app-updates.json`:

```json
{
  "id": "vendy-v1.0.1-android",
  "version": "1.0.1",
  "buildNumber": "2",
  "platform": "android",
  "filename": "vendy-v1.0.1-android.zip",
  "checksum": "sha256:your_calculated_checksum",
  "size": 5242880,
  "isMandatory": false,
  "description": "Bug fixes and improvements",
  "releaseNotes": "- Fixed login issues\n- Improved performance",
  "minAppVersion": "1.0.0",
  "createdAt": "2024-01-20T10:00:00.000Z",
  "isActive": true
}
```

## API Endpoints

### Check for Updates
```
POST /api/v1/app/updates/check
```

Request:
```json
{
  "currentVersion": "1.0.0",
  "currentBuildNumber": "1",
  "platform": "android",
  "deviceInfo": {
    "model": "Pixel 6",
    "systemVersion": "13",
    "uniqueId": "device_id"
  }
}
```

Response:
```json
{
  "success": true,
  "updateAvailable": true,
  "bundle": {
    "version": "1.0.1",
    "downloadUrl": "/api/v1/app/updates/download/vendy-v1.0.1-android",
    "checksum": "sha256:...",
    "size": 5242880,
    "isMandatory": false,
    "description": "Bug fixes and improvements"
  }
}
```

### Download Bundle
```
GET /api/v1/app/updates/download/:updateId
```

Returns the update bundle as a zip file.

### Track Analytics
```
POST /api/v1/app/updates/analytics
```

Request:
```json
{
  "event": "download_completed",
  "updateId": "vendy-v1.0.1-android",
  "platform": "android",
  "metadata": {}
}
```

## Configuration Options

### OTA Service Configuration

```typescript
const config = {
  checkFrequency: 'on_app_resume', // 'on_app_start' | 'on_app_resume' | 'manual' | 'periodic'
  updatePolicy: UpdatePolicy.ON_APP_RESUME, // IMMEDIATE | ON_APP_RESUME | MANUAL | SILENT
  autoDownload: true, // Automatically download available updates
  autoInstall: false, // Automatically install downloaded updates
  enableAutoCheck: true, // Enable automatic update checking
  updateCheckInterval: 30 * 60 * 1000, // Check interval in milliseconds
  apiBaseUrl: 'https://your-backend.com'
};
```

### Update Policies

- **IMMEDIATE**: Install immediately after download
- **ON_APP_RESUME**: Install when app resumes from background
- **MANUAL**: User must manually confirm installation
- **SILENT**: Download and install silently

## Security Features

1. **Bundle Verification**: SHA256 checksum validation
2. **Version Compatibility**: Ensures updates are compatible with current app version
3. **Secure Downloads**: HTTPS-only bundle distribution
4. **Rate Limiting**: Prevents abuse of update endpoints
5. **Rollback Support**: Automatic rollback on installation failures

## Monitoring and Analytics

The system tracks:
- Update check requests
- Download events
- Installation success/failure
- User interactions
- Device and version information

## Troubleshooting

### Common Issues

1. **Update Check Fails**
   - Verify backend URL configuration
   - Check network connectivity
   - Ensure API endpoints are accessible

2. **Download Fails**
   - Check bundle file exists in storage directory
   - Verify file permissions
   - Check available storage space

3. **Installation Fails**
   - Check bundle integrity (checksum)
   - Verify app has write permissions
   - Check for sufficient storage space

### Debug Logging

Enable debug logging in development:
```typescript
// In your app initialization
if (__DEV__) {
  otaService.updateConfig({ enableAnalytics: true });
}
```

## Best Practices

1. **Testing**: Always test updates on staging environment first
2. **Gradual Rollout**: Use percentage-based rollouts for major updates
3. **Monitoring**: Monitor update success rates and user feedback
4. **Rollback Plan**: Always have a rollback strategy for critical issues
5. **Bundle Size**: Keep update bundles as small as possible
6. **Mandatory Updates**: Use sparingly, only for critical security fixes

## Future Enhancements

- [ ] Differential updates (delta patches)
- [ ] A/B testing integration
- [ ] Advanced rollout strategies
- [ ] Update scheduling
- [ ] Offline update support
- [ ] Multi-environment support
