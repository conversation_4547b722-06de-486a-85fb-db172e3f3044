/**
 * Airtime Purchase Controller
 * 
 * Secure controller for MTN airtime purchases via VTpass API with comprehensive
 * validation, security measures, transaction management, and error handling.
 * 
 * Features:
 * - Multi-layer input validation
 * - Security middleware integration
 * - Transaction state management
 * - Comprehensive error handling
 * - Audit logging and monitoring
 * - Balance verification
 * - Fraud detection integration
 * - Real-time notifications
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const { body, validationResult } = require('express-validator');
const vtpassService = require('../services/vtpassService');
const { vtpassErrorHandler } = require('../utils/vtpassErrorHandler');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');
const notificationService = require('../services/notificationService');

// Lazy load VTU provider manager to avoid initialization issues
let vtuProviderManager = null;
const getVtuProviderManager = () => {
  if (!vtuProviderManager) {
    vtuProviderManager = require('../services/vtuProviderManager');
  }
  return vtuProviderManager;
};

/**
 * Airtime Controller Class
 * Handles all airtime purchase operations with enterprise-grade security
 */
class AirtimeController {
  constructor() {
    this.supabase = getSupabase();
  }

  /**
   * Validation rules for airtime purchase
   * Comprehensive input validation with security checks
   */
  static getValidationRules() {
    return [
      body('phone')
        .notEmpty()
        .withMessage('Phone number is required')
        .isLength({ min: 10, max: 15 })
        .withMessage('Phone number must be between 10 and 15 digits')
        .matches(/^[\d+\-\s()]+$/)
        .withMessage('Phone number contains invalid characters')
        .custom((value) => {
          // Remove all non-digit characters for validation
          const cleanPhone = value.replace(/\D/g, '');
          if (cleanPhone.length < 10 || cleanPhone.length > 15) {
            throw new Error('Invalid phone number length');
          }
          return true;
        }),

      body('network')
        .optional()
        .isIn(['mtn', 'glo', 'airtel', 'etisalat'])
        .withMessage('Network must be one of: mtn, glo, airtel, etisalat'),

      body('amount')
        .notEmpty()
        .withMessage('Amount is required')
        .isNumeric()
        .withMessage('Amount must be a number')
        .custom((value) => {
          const amount = parseFloat(value);
          if (amount <= 0) {
            throw new Error('Amount must be greater than 0');
          }
          if (amount < 50) {
            throw new Error('Minimum amount is ₦50');
          }
          if (amount > 50000) {
            throw new Error('Maximum amount is ₦50,000');
          }
          // Check for reasonable decimal places
          if (amount.toString().includes('.') && amount.toString().split('.')[1].length > 2) {
            throw new Error('Amount cannot have more than 2 decimal places');
          }
          return true;
        }),

      body('pin')
        .optional()
        .isLength({ min: 4, max: 6 })
        .withMessage('PIN must be 4-6 digits')
        .isNumeric()
        .withMessage('PIN must contain only numbers'),

      body('saveRecipient')
        .optional()
        .isBoolean()
        .withMessage('saveRecipient must be a boolean'),

      body('recipientName')
        .optional()
        .isLength({ max: 100 })
        .withMessage('Recipient name cannot exceed 100 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('Recipient name can only contain letters and spaces')
    ];
  }

  /**
   * Purchase MTN airtime
   * Main endpoint for airtime purchases with full security and validation
   * 
   * @route POST /api/v1/airtime/purchase
   * @access Private (Authenticated users only)
   */
  async purchaseAirtime(req, res) {
    const requestId = req.headers['x-request-id'] || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      logger.info('🎯 [AIRTIME_CONTROLLER] Purchase request initiated:', {
        requestId,
        userId: req.user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Validate request inputs
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        logger.warn('⚠️ [AIRTIME_CONTROLLER] Validation failed:', {
          requestId,
          userId: req.user.id,
          errors: errors.array()
        });

        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
          requestId
        });
      }

      const { phone, amount, pin, network, saveRecipient = false, recipientName } = req.body;
      const userId = req.user.id;

      // Verify user PIN if provided (for additional security)
      if (pin) {
        const pinValid = await this.verifyUserPin(userId, pin);
        if (!pinValid) {
          logger.warn('🚫 [AIRTIME_CONTROLLER] Invalid PIN provided:', {
            requestId,
            userId,
            ip: req.ip
          });

          return res.status(401).json({
            success: false,
            message: 'Invalid PIN',
            code: 'INVALID_PIN',
            requestId
          });
        }
      }

      // Detect network from phone number (or validate specified network)
      const phoneValidation = vtpassService.detectNetwork(phone);
      if (!phoneValidation.valid) {
        logger.warn('⚠️ [AIRTIME_CONTROLLER] Invalid phone number:', {
          requestId,
          userId,
          phone: phone.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3'),
          error: phoneValidation.error
        });

        return res.status(400).json({
          success: false,
          message: phoneValidation.error,
          code: 'INVALID_PHONE',
          requestId
        });
      }

      // If network was specified, validate it matches detected network
      if (network && phoneValidation.network !== network) {
        logger.warn('⚠️ [AIRTIME_CONTROLLER] Network mismatch:', {
          requestId,
          userId,
          phone: phone.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3'),
          specifiedNetwork: network,
          detectedNetwork: phoneValidation.network
        });

        return res.status(400).json({
          success: false,
          message: `Phone number belongs to ${phoneValidation.networkName}, not ${network.toUpperCase()}`,
          code: 'NETWORK_MISMATCH',
          detectedNetwork: phoneValidation.network,
          detectedNetworkName: phoneValidation.networkName,
          requestId
        });
      }

      // Check user balance
      const userBalance = await this.getUserBalance(userId);
      const transactionAmount = parseFloat(amount);
      
      if (userBalance < transactionAmount) {
        logger.warn('💰 [AIRTIME_CONTROLLER] Insufficient balance:', {
          requestId,
          userId,
          balance: userBalance,
          requestedAmount: transactionAmount
        });

        return res.status(400).json({
          success: false,
          message: 'Insufficient balance',
          code: 'INSUFFICIENT_BALANCE',
          currentBalance: userBalance,
          requiredAmount: transactionAmount,
          requestId
        });
      }

      // Prepare purchase data with network information
      const purchaseData = {
        userId,
        phone: phoneValidation.normalized,
        amount: transactionAmount,
        network: phoneValidation.network,
        networkName: phoneValidation.networkName,
        serviceId: phoneValidation.serviceId,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        requestId,
        riskScore: req.riskScore || 0,
        riskFactors: req.riskFactors || []
      };

      logger.info('💳 [AIRTIME_CONTROLLER] Processing purchase:', {
        requestId,
        userId,
        amount: transactionAmount,
        phone: phoneValidation.normalized.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3'),
        network: phoneValidation.network,
        networkName: phoneValidation.networkName,
        riskScore: purchaseData.riskScore
      });

      // Process the purchase through VTU Provider Manager (with automatic failover)
      const providerManager = getVtuProviderManager();
      const result = await providerManager.purchaseAirtime(purchaseData);

      // Save recipient if requested
      if (saveRecipient && recipientName) {
        await this.saveRecipient(userId, phoneValidation.normalized, recipientName);
      }

      // Send success notification
      if (result.success) {
        await this.sendPurchaseNotification(userId, {
          type: 'airtime',
          amount: transactionAmount,
          recipient: phoneValidation.normalized,
          network: phoneValidation.network,
          provider: phoneValidation.networkName,
          transactionId: result.transactionId
        });
      }

      logger.info('✅ [AIRTIME_CONTROLLER] Purchase completed:', {
        requestId,
        userId,
        transactionId: result.transactionId,
        status: result.status,
        success: result.success
      });

      return res.status(200).json({
        success: result.success,
        message: result.message,
        data: {
          transactionId: result.transactionId,
          status: result.status,
          amount: transactionAmount,
          recipient: phoneValidation.normalized,
          network: phoneValidation.network,
          networkName: phoneValidation.networkName,
          provider: phoneValidation.networkName,
          requestId,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      // Handle errors using the comprehensive error handler
      const errorResponse = vtpassErrorHandler.handleError(error, {
        requestId,
        userId: req.user?.id,
        amount: req.body?.amount,
        phone: req.body?.phone,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      logger.error('❌ [AIRTIME_CONTROLLER] Purchase failed:', {
        requestId,
        userId: req.user?.id,
        error: error.message,
        stack: error.stack
      });

      return res.status(500).json({
        ...errorResponse,
        requestId
      });
    }
  }

  /**
   * Get user's current balance
   * 
   * @param {string} userId - User ID
   * @returns {number} User balance
   */
  async getUserBalance(userId) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('balance')
        .eq('id', userId)
        .single();

      if (error) {
        logger.error('❌ [AIRTIME_CONTROLLER] Balance query error:', error);
        throw new Error('Failed to retrieve user balance');
      }

      return parseFloat(data.balance) || 0;
    } catch (error) {
      logger.error('❌ [AIRTIME_CONTROLLER] Get balance error:', error);
      throw error;
    }
  }

  /**
   * Verify user PIN for additional security
   * 
   * @param {string} userId - User ID
   * @param {string} pin - PIN to verify
   * @returns {boolean} PIN validity
   */
  async verifyUserPin(userId, pin) {
    try {
      const bcrypt = require('bcryptjs');
      
      const { data, error } = await this.supabase
        .from('users')
        .select('pin')
        .eq('id', userId)
        .single();

      if (error || !data) {
        logger.error('❌ [AIRTIME_CONTROLLER] PIN verification query error:', error);
        return false;
      }

      return await bcrypt.compare(pin, data.pin);
    } catch (error) {
      logger.error('❌ [AIRTIME_CONTROLLER] PIN verification error:', error);
      return false;
    }
  }

  /**
   * Save recipient for future use
   * 
   * @param {string} userId - User ID
   * @param {string} phone - Phone number
   * @param {string} name - Recipient name
   */
  async saveRecipient(userId, phone, name) {
    try {
      // Check if recipient already exists
      const { data: existing } = await this.supabase
        .from('saved_recipients')
        .select('id')
        .eq('user_id', userId)
        .eq('phone', phone)
        .single();

      if (existing) {
        // Update existing recipient
        await this.supabase
          .from('saved_recipients')
          .update({ name, updated_at: new Date().toISOString() })
          .eq('id', existing.id);
      } else {
        // Create new recipient
        await this.supabase
          .from('saved_recipients')
          .insert([{
            user_id: userId,
            phone,
            name,
            type: 'airtime',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }]);
      }

      logger.info('✅ [AIRTIME_CONTROLLER] Recipient saved:', {
        userId,
        phone: phone.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3'),
        name
      });
    } catch (error) {
      logger.error('❌ [AIRTIME_CONTROLLER] Save recipient error:', error);
      // Don't throw error as this is not critical for the transaction
    }
  }

  /**
   * Send purchase notification to user
   * 
   * @param {string} userId - User ID
   * @param {Object} purchaseData - Purchase details
   */
  async sendPurchaseNotification(userId, purchaseData) {
    try {
      await notificationService.sendPurchaseNotification(userId, purchaseData);
      logger.info('📱 [AIRTIME_CONTROLLER] Notification sent:', {
        userId,
        type: purchaseData.type,
        amount: purchaseData.amount
      });
    } catch (error) {
      logger.error('❌ [AIRTIME_CONTROLLER] Notification error:', error);
      // Don't throw error as this is not critical for the transaction
    }
  }

  /**
   * Get user's airtime purchase history
   * 
   * @route GET /api/v1/airtime/history
   * @access Private
   */
  async getPurchaseHistory(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 20, status } = req.query;
      const offset = (page - 1) * limit;

      let query = this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .eq('type', 'airtime')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query;

      if (error) {
        logger.error('❌ [AIRTIME_CONTROLLER] History query error:', error);
        return res.status(500).json({
          success: false,
          message: 'Failed to retrieve purchase history'
        });
      }

      return res.status(200).json({
        success: true,
        data: {
          transactions: data,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: data.length
          }
        }
      });
    } catch (error) {
      logger.error('❌ [AIRTIME_CONTROLLER] Get history error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve purchase history'
      });
    }
  }
}

// Create singleton instance
const airtimeController = new AirtimeController();

module.exports = {
  purchaseAirtime: airtimeController.purchaseAirtime.bind(airtimeController),
  getPurchaseHistory: airtimeController.getPurchaseHistory.bind(airtimeController),
  getValidationRules: AirtimeController.getValidationRules,
  airtimeController
};
