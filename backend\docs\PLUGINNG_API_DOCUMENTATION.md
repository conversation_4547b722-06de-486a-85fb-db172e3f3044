# PluginNG API Integration Documentation

## Overview

This document provides comprehensive technical documentation for the PluginNG API integration in the PayVendy VTU application. PluginNG serves as a backup provider for airtime purchases when VTpass is unavailable.

## Table of Contents

1. [API Configuration](#api-configuration)
2. [Service Architecture](#service-architecture)
3. [Request/Response Format](#requestresponse-format)
4. [Error Handling](#error-handling)
5. [Network Support](#network-support)
6. [Security Implementation](#security-implementation)
7. [Monitoring and Logging](#monitoring-and-logging)

## API Configuration

### Base Configuration

```javascript
const config = {
  baseUrl: 'https://pluginng.com/api',
  timeout: 30000,
  maxRetries: 3,
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
    'Accept': 'application/json',
    'Authorization': 'Bearer YOUR_API_TOKEN'
  }
};
```

### Endpoints

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/purchase/airtime` | POST | Purchase airtime |
| `/balance` | GET | Check account balance |
| `/query/transaction` | POST | Query transaction status |

## Service Architecture

### Class Structure

```javascript
class PluginNGService {
  constructor()              // Initialize service
  makeRequest()             // HTTP request handler
  createTransaction()       // Database transaction creation
  updateTransaction()       // Database transaction update
  processPurchaseResponse() // Response processing
  purchaseAirtime()        // Main purchase method
  queryTransaction()       // Transaction query
  getBalance()            // Balance inquiry
}
```

### Provider Manager Integration

```javascript
// Provider registration
{
  id: 'pluginng',
  name: 'PluginNG',
  priority: PROVIDER_PRIORITY.BACKUP,
  status: PROVIDER_STATUS.ACTIVE,
  service: pluginNGService,
  capabilities: {
    airtime: true,
    supportedNetworks: ['mtn', 'glo', 'airtel', 'etisalat']
  }
}
```

## Request/Response Format

### Airtime Purchase Request

```javascript
// Internal request format
{
  userId: "uuid",
  phone: "08012345678",
  amount: 100,
  network: "mtn",
  ipAddress: "***********",
  userAgent: "PayVendy-API"
}

// PluginNG API payload (form-data)
{
  amount: "100",
  phonenumber: "08012345678", 
  subcategory_id: "10",
  custom_reference: "png_1234567890_abc123",
  ported: "yes" // Optional for MTN
}
```

### Airtime Purchase Response

```javascript
// Expected PluginNG response
{
  status: "success",
  message: "Transaction successful",
  reference: "PNG123456789",
  transaction_id: "TXN_ABC123",
  amount: "100.00",
  phone: "08012345678"
}

// Standardized internal response
{
  success: true,
  status: "completed",
  transactionId: "uuid",
  pluginngTransactionId: "PNG123456789",
  message: "Transaction successful",
  transaction: { /* full transaction object */ }
}
```

### Balance Inquiry

```javascript
// Request
GET /balance
Authorization: Bearer TOKEN

// Response
{
  status: "success",
  balance: "5000.00",
  currency: "NGN"
}
```

### Transaction Query

```javascript
// Request
{
  reference: "PNG123456789"
}

// Response
{
  status: "success",
  transaction_status: "completed",
  reference: "PNG123456789",
  amount: "100.00",
  phone: "08012345678"
}
```

## Error Handling

### Error Categories

```javascript
const ERROR_CATEGORIES = {
  AUTHENTICATION: 'authentication_error',
  VALIDATION: 'validation_error', 
  INSUFFICIENT_BALANCE: 'insufficient_balance',
  NETWORK_ERROR: 'network_error',
  SERVER_ERROR: 'server_error',
  RATE_LIMIT: 'rate_limit_error',
  INVALID_PHONE: 'invalid_phone',
  TRANSACTION_FAILED: 'transaction_failed',
  TIMEOUT: 'timeout_error',
  UNKNOWN: 'unknown_error'
};
```

### Error Response Format

```javascript
{
  code: "INVALID_PHONE",
  category: "invalid_phone",
  severity: "medium",
  message: "Invalid phone number format",
  userMessage: "Please enter a valid phone number",
  retryable: false,
  requestId: "req_123",
  timestamp: "2025-07-11T10:30:00Z",
  provider: "pluginng"
}
```

### HTTP Status Code Mapping

| Status | Error Code | Category | Retryable |
|--------|------------|----------|-----------|
| 400 | INVALID_REQUEST | validation_error | No |
| 401 | UNAUTHORIZED | authentication_error | No |
| 403 | INVALID_TOKEN | authentication_error | No |
| 429 | RATE_LIMIT_EXCEEDED | rate_limit_error | Yes |
| 500+ | SERVER_ERROR | server_error | Yes |

## Network Support

### Supported Networks

```javascript
const networks = {
  mtn: {
    subcategoryId: '10',
    name: 'MTN Nigeria',
    prefixes: ['0803', '0806', '0703', '0706', '0813', '0816', '0810', '0814', '0903', '0906', '0913', '0916']
  },
  glo: {
    subcategoryId: '11',
    name: 'Glo Nigeria', 
    prefixes: ['0805', '0807', '0705', '0815', '0811', '0905', '0915']
  },
  airtel: {
    subcategoryId: '12',
    name: 'Airtel Nigeria',
    prefixes: ['0802', '0808', '0708', '0812', '0701', '0902', '0907', '0901', '0904', '0912']
  },
  etisalat: {
    subcategoryId: '13',
    name: '9mobile Nigeria',
    prefixes: ['0809', '0818', '0817', '0909', '0908']
  }
};
```

### Phone Number Validation

```javascript
// Validation process
1. Remove non-digit characters
2. Handle different formats (234xxx, 0xxx, xxx)
3. Normalize to international format (234xxxxxxxxxx)
4. Detect network from prefix
5. Validate against expected network
6. Return validation result with network info
```

## Security Implementation

### Authentication

```javascript
// Bearer token authentication
headers: {
  'Authorization': `Bearer ${process.env.PLUGINNG_API_TOKEN}`
}
```

### Request Security

```javascript
// Security measures
- Input validation and sanitization
- Phone number normalization
- Amount validation
- Rate limiting
- Request timeout
- Retry with exponential backoff
- Comprehensive logging
```

### Data Protection

```javascript
// Sensitive data handling
- API tokens stored in environment variables
- Phone numbers masked in logs
- Transaction data encrypted in database
- Secure HTTPS communication
- Request/response logging for audit
```

## Monitoring and Logging

### Log Levels

```javascript
// Log categories
INFO:  Normal operations
WARN:  Recoverable errors
ERROR: Critical failures
DEBUG: Detailed debugging info
```

### Key Metrics

```javascript
// Performance metrics
- Success rate
- Average response time
- Error rate by category
- Transaction volume
- Failover frequency
- Provider availability
```

### Database Logging

```javascript
// Transaction logs table
pluginng_transaction_logs {
  id: UUID,
  transaction_id: UUID,
  request_id: VARCHAR,
  request_type: VARCHAR,
  request_payload: JSONB,
  response_payload: JSONB,
  response_code: VARCHAR,
  http_status: INTEGER,
  duration_ms: INTEGER,
  retry_attempt: INTEGER,
  created_at: TIMESTAMP
}
```

### Monitoring Queries

```sql
-- Success rate monitoring
SELECT 
  COUNT(*) as total,
  COUNT(*) FILTER (WHERE status = 'completed') as successful,
  ROUND(COUNT(*) FILTER (WHERE status = 'completed') * 100.0 / COUNT(*), 2) as success_rate
FROM transactions 
WHERE provider_type = 'pluginng' 
AND created_at >= NOW() - INTERVAL '1 hour';

-- Error analysis
SELECT 
  failure_reason,
  COUNT(*) as count
FROM transactions 
WHERE provider_type = 'pluginng' 
AND status = 'failed'
AND created_at >= NOW() - INTERVAL '24 hours'
GROUP BY failure_reason
ORDER BY count DESC;
```

## Integration Examples

### Basic Purchase Flow

```javascript
// 1. Validate input
const phoneValidation = pluginNGConfig.validatePhone(phone);
const amountValidation = pluginNGConfig.validateAmount(amount);

// 2. Create transaction
const transaction = await pluginNGService.createTransaction({
  userId, phone, amount, network, ipAddress, userAgent
});

// 3. Make API request
const response = await pluginNGService.makeRequest('POST', endpoint, payload);

// 4. Process response
const result = await pluginNGService.processPurchaseResponse(
  transaction.id, response, phoneValidation
);

// 5. Return result
return result;
```

## Best Practices

### 1. Error Handling
- Always use standardized error handling
- Implement proper retry logic
- Log errors with context
- Provide user-friendly messages

### 2. Performance
- Use connection pooling
- Implement request caching where appropriate
- Monitor response times
- Set appropriate timeouts

### 3. Security
- Validate all inputs
- Use secure communication (HTTPS)
- Implement rate limiting
- Log security events

### 4. Monitoring
- Track key metrics
- Set up alerts for failures
- Monitor provider performance
- Implement health checks
