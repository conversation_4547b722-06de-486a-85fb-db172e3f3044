/**
 * Security Middleware for Data Plan Manager
 * 
 * Middleware for securing API endpoints and WebSocket connections
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const securityConfig = require('../config/security.config');
const logger = require('../../utils/logger');

/**
 * Rate limiting middleware
 */
const rateLimitMiddleware = (type = 'apiCalls') => {
  return (req, res, next) => {
    const identifier = req.ip || req.connection.remoteAddress;
    const rateLimit = securityConfig.checkRateLimit(identifier, type);
    
    if (!rateLimit.allowed) {
      securityConfig.logSecurityEvent('RATE_LIMIT_EXCEEDED', {
        ip: identifier,
        type,
        reason: rateLimit.reason,
        userAgent: req.headers['user-agent']
      });
      
      return res.status(429).json({
        success: false,
        error: 'Rate limit exceeded',
        retryAfter: rateLimit.retryAfter,
        timestamp: new Date().toISOString()
      });
    }
    
    // Add rate limit headers
    res.set({
      'X-RateLimit-Remaining': rateLimit.remaining || 0,
      'X-RateLimit-Reset': new Date(Date.now() + 60000).toISOString()
    });
    
    next();
  };
};

/**
 * IP blocking middleware
 */
const ipBlockingMiddleware = (req, res, next) => {
  const ip = req.ip || req.connection.remoteAddress;
  
  if (securityConfig.isIPBlocked(ip)) {
    securityConfig.logSecurityEvent('BLOCKED_IP_ACCESS_ATTEMPT', {
      ip,
      url: req.originalUrl,
      userAgent: req.headers['user-agent']
    });
    
    return res.status(403).json({
      success: false,
      error: 'Access denied',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

/**
 * Security headers middleware
 */
const securityHeadersMiddleware = (req, res, next) => {
  const headers = securityConfig.getSecurityHeaders();
  res.set(headers);
  next();
};

/**
 * Input sanitization middleware
 */
const inputSanitizationMiddleware = (req, res, next) => {
  // Sanitize query parameters
  if (req.query) {
    for (const [key, value] of Object.entries(req.query)) {
      if (typeof value === 'string') {
        req.query[key] = securityConfig.sanitizeInput(value);
      }
    }
  }
  
  // Sanitize body parameters
  if (req.body && typeof req.body === 'object') {
    for (const [key, value] of Object.entries(req.body)) {
      if (typeof value === 'string') {
        req.body[key] = securityConfig.sanitizeInput(value);
      }
    }
  }
  
  next();
};

/**
 * Origin validation middleware
 */
const originValidationMiddleware = (req, res, next) => {
  if (!securityConfig.validateRequestOrigin(req)) {
    securityConfig.logSecurityEvent('INVALID_ORIGIN', {
      origin: req.headers.origin,
      referer: req.headers.referer,
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });
    
    return res.status(403).json({
      success: false,
      error: 'Invalid request origin',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

/**
 * Admin authentication middleware
 */
const adminAuthMiddleware = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      securityConfig.logSecurityEvent('MISSING_ADMIN_TOKEN', {
        ip: req.ip,
        url: req.originalUrl,
        userAgent: req.headers['user-agent']
      });
      
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        timestamp: new Date().toISOString()
      });
    }
    
    const token = authHeader.substring(7);
    
    // Validate token format
    if (!securityConfig.validateApiKeyFormat(token)) {
      securityConfig.logSecurityEvent('INVALID_ADMIN_TOKEN_FORMAT', {
        ip: req.ip,
        tokenPreview: securityConfig.maskSensitiveData(token, 'token'),
        userAgent: req.headers['user-agent']
      });
      
      return res.status(401).json({
        success: false,
        error: 'Invalid token format',
        timestamp: new Date().toISOString()
      });
    }
    
    // TODO: Implement actual token validation with your auth system
    // For now, we'll accept any properly formatted JWT token
    // You should replace this with your actual admin authentication logic
    
    // Add user info to request
    req.user = {
      id: 'admin',
      role: 'admin',
      permissions: ['data_plan_management']
    };
    
    next();
  } catch (error) {
    securityConfig.logSecurityEvent('ADMIN_AUTH_ERROR', {
      ip: req.ip,
      error: error.message,
      userAgent: req.headers['user-agent']
    });
    
    return res.status(500).json({
      success: false,
      error: 'Authentication error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Request logging middleware
 */
const requestLoggingMiddleware = (req, res, next) => {
  const requestId = securityConfig.generateSecureRequestId();
  req.requestId = requestId;
  
  // Log request details (excluding sensitive data)
  logger.info('📥 [DATA_PLAN_API] Request received:', {
    requestId,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    contentType: req.headers['content-type'],
    hasAuth: !!req.headers.authorization
  });
  
  // Log response when finished
  const originalSend = res.send;
  res.send = function(data) {
    logger.info('📤 [DATA_PLAN_API] Response sent:', {
      requestId,
      statusCode: res.statusCode,
      responseSize: data ? data.length : 0
    });
    originalSend.call(this, data);
  };
  
  next();
};

/**
 * Error handling middleware
 */
const errorHandlingMiddleware = (error, req, res, next) => {
  const requestId = req.requestId || 'unknown';
  
  securityConfig.logSecurityEvent('API_ERROR', {
    requestId,
    error: error.message,
    stack: error.stack,
    ip: req.ip,
    url: req.originalUrl
  });
  
  // Don't expose internal errors in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(500).json({
    success: false,
    error: isDevelopment ? error.message : 'Internal server error',
    requestId,
    timestamp: new Date().toISOString()
  });
};

/**
 * WebSocket security middleware
 */
const websocketSecurityMiddleware = (ws, req) => {
  const ip = req.connection.remoteAddress;
  
  // Check if IP is blocked
  if (securityConfig.isIPBlocked(ip)) {
    securityConfig.logSecurityEvent('BLOCKED_IP_WEBSOCKET_ATTEMPT', { ip });
    ws.close(1008, 'Access denied');
    return false;
  }
  
  // Check rate limit
  const rateLimit = securityConfig.checkRateLimit(ip, 'apiCalls');
  if (!rateLimit.allowed) {
    securityConfig.logSecurityEvent('WEBSOCKET_RATE_LIMIT_EXCEEDED', { ip });
    ws.close(1008, 'Rate limit exceeded');
    return false;
  }
  
  // Validate origin
  if (!securityConfig.validateRequestOrigin(req)) {
    securityConfig.logSecurityEvent('WEBSOCKET_INVALID_ORIGIN', {
      origin: req.headers.origin,
      ip
    });
    ws.close(1008, 'Invalid origin');
    return false;
  }
  
  return true;
};

module.exports = {
  rateLimitMiddleware,
  ipBlockingMiddleware,
  securityHeadersMiddleware,
  inputSanitizationMiddleware,
  originValidationMiddleware,
  adminAuthMiddleware,
  requestLoggingMiddleware,
  errorHandlingMiddleware,
  websocketSecurityMiddleware
};
