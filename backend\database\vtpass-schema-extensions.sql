-- =====================================================
-- VTPASS SCHEMA EXTENSIONS
-- =====================================================
-- Database schema extensions to support VTpass MTN VTU API integration
-- Adds VTpass-specific fields, indexes, and functions for optimal performance
-- 
-- Author: PayVendy Development Team
-- Version: 1.0.0
-- Date: 2025-07-11

-- =====================================================
-- TRANSACTION TABLE EXTENSIONS
-- =====================================================

-- Add VTpass-specific columns to existing transactions table
DO $$ 
BEGIN
    -- Add provider_type column to distinguish between different VTU providers
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'provider_type'
    ) THEN
        ALTER TABLE transactions ADD COLUMN provider_type VARCHAR(50) DEFAULT 'vtpass';
    END IF;

    -- Add provider_transaction_id for external reference tracking
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'provider_transaction_id'
    ) THEN
        ALTER TABLE transactions ADD COLUMN provider_transaction_id VARCHAR(100);
    END IF;

    -- Add commission tracking for VTpass transactions
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'commission_amount'
    ) THEN
        ALTER TABLE transactions ADD COLUMN commission_amount DECIMAL(15,4) DEFAULT 0.0000;
    END IF;

    -- Add commission rate tracking
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'commission_rate'
    ) THEN
        ALTER TABLE transactions ADD COLUMN commission_rate DECIMAL(5,4) DEFAULT 0.0000;
    END IF;

    -- Add total amount after commission
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'total_amount'
    ) THEN
        ALTER TABLE transactions ADD COLUMN total_amount DECIMAL(15,2);
    END IF;

    -- Add retry count for failed transactions
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'retry_count'
    ) THEN
        ALTER TABLE transactions ADD COLUMN retry_count INTEGER DEFAULT 0;
    END IF;

    -- Add last retry timestamp
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'last_retry_at'
    ) THEN
        ALTER TABLE transactions ADD COLUMN last_retry_at TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add completion timestamp
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'completed_at'
    ) THEN
        ALTER TABLE transactions ADD COLUMN completed_at TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add failure reason categorization
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'failure_reason'
    ) THEN
        ALTER TABLE transactions ADD COLUMN failure_reason VARCHAR(100);
    END IF;

    -- Add IP address tracking for security
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'ip_address'
    ) THEN
        ALTER TABLE transactions ADD COLUMN ip_address INET;
    END IF;

    -- Add user agent tracking
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'user_agent'
    ) THEN
        ALTER TABLE transactions ADD COLUMN user_agent TEXT;
    END IF;
END $$;

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Index for VTpass provider transactions
CREATE INDEX IF NOT EXISTS idx_transactions_provider_type 
ON transactions(provider_type) 
WHERE provider_type = 'vtpass';

-- Index for external reference lookups
CREATE INDEX IF NOT EXISTS idx_transactions_provider_transaction_id 
ON transactions(provider_transaction_id) 
WHERE provider_transaction_id IS NOT NULL;

-- Composite index for user transactions by provider
CREATE INDEX IF NOT EXISTS idx_transactions_user_provider_status 
ON transactions(user_id, provider_type, status, created_at DESC);

-- Index for pending transactions that need retry
CREATE INDEX IF NOT EXISTS idx_transactions_pending_retry 
ON transactions(status, last_retry_at, retry_count) 
WHERE status = 'pending' OR status = 'failed';

-- Index for transaction reference lookups
CREATE INDEX IF NOT EXISTS idx_transactions_reference_provider 
ON transactions(reference, provider_type);

-- Index for date-based queries with provider filter
CREATE INDEX IF NOT EXISTS idx_transactions_date_provider 
ON transactions(created_at DESC, provider_type, status);

-- Index for amount-based queries (for reporting)
CREATE INDEX IF NOT EXISTS idx_transactions_amount_date 
ON transactions(amount, created_at DESC) 
WHERE status = 'completed';

-- Index for IP address tracking (security)
CREATE INDEX IF NOT EXISTS idx_transactions_ip_address 
ON transactions(ip_address, created_at DESC);

-- =====================================================
-- VTPASS TRANSACTION LOG TABLE
-- =====================================================

-- Create dedicated table for VTpass API request/response logging
CREATE TABLE IF NOT EXISTS vtpass_transaction_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    request_id VARCHAR(100) NOT NULL,
    request_type VARCHAR(50) NOT NULL, -- 'purchase', 'query', 'balance'
    request_payload JSONB NOT NULL,
    response_payload JSONB,
    response_code VARCHAR(10),
    response_message TEXT,
    http_status INTEGER,
    duration_ms INTEGER,
    retry_attempt INTEGER DEFAULT 1,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for VTpass logs
CREATE INDEX IF NOT EXISTS idx_vtpass_logs_transaction_id 
ON vtpass_transaction_logs(transaction_id);

CREATE INDEX IF NOT EXISTS idx_vtpass_logs_request_id 
ON vtpass_transaction_logs(request_id);

CREATE INDEX IF NOT EXISTS idx_vtpass_logs_request_type_date 
ON vtpass_transaction_logs(request_type, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_vtpass_logs_response_code 
ON vtpass_transaction_logs(response_code, created_at DESC);

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to get VTpass transaction statistics
CREATE OR REPLACE FUNCTION get_vtpass_stats(
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '24 hours',
    end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    total_transactions BIGINT,
    successful_transactions BIGINT,
    failed_transactions BIGINT,
    pending_transactions BIGINT,
    total_amount DECIMAL(15,2),
    total_commission DECIMAL(15,4),
    success_rate DECIMAL(5,2),
    average_amount DECIMAL(15,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_transactions,
        COUNT(*) FILTER (WHERE status = 'completed') as successful_transactions,
        COUNT(*) FILTER (WHERE status = 'failed') as failed_transactions,
        COUNT(*) FILTER (WHERE status = 'pending') as pending_transactions,
        COALESCE(SUM(amount) FILTER (WHERE status = 'completed'), 0) as total_amount,
        COALESCE(SUM(commission_amount) FILTER (WHERE status = 'completed'), 0) as total_commission,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                ROUND((COUNT(*) FILTER (WHERE status = 'completed') * 100.0 / COUNT(*)), 2)
            ELSE 0 
        END as success_rate,
        CASE 
            WHEN COUNT(*) FILTER (WHERE status = 'completed') > 0 THEN 
                ROUND(AVG(amount) FILTER (WHERE status = 'completed'), 2)
            ELSE 0 
        END as average_amount
    FROM transactions 
    WHERE provider_type = 'vtpass' 
    AND created_at BETWEEN start_date AND end_date;
END;
$$ LANGUAGE plpgsql;

-- Function to get pending transactions for retry
CREATE OR REPLACE FUNCTION get_pending_vtpass_transactions(
    max_retry_count INTEGER DEFAULT 3,
    retry_delay_minutes INTEGER DEFAULT 5
)
RETURNS TABLE (
    id UUID,
    reference VARCHAR(100),
    amount DECIMAL(15,2),
    recipient VARCHAR(255),
    retry_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    last_retry_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.reference,
        t.amount,
        t.recipient,
        t.retry_count,
        t.created_at,
        t.last_retry_at
    FROM transactions t
    WHERE t.provider_type = 'vtpass'
    AND t.status = 'pending'
    AND t.retry_count < max_retry_count
    AND (
        t.last_retry_at IS NULL 
        OR t.last_retry_at < NOW() - (retry_delay_minutes || ' minutes')::INTERVAL
    )
    ORDER BY t.created_at ASC
    LIMIT 100;
END;
$$ LANGUAGE plpgsql;

-- Function to update transaction status with automatic timestamp
CREATE OR REPLACE FUNCTION update_vtpass_transaction_status(
    transaction_id UUID,
    new_status VARCHAR(20),
    provider_tx_id VARCHAR(100) DEFAULT NULL,
    commission_amt DECIMAL(15,4) DEFAULT NULL,
    failure_reason_val VARCHAR(100) DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    update_data JSONB := '{}';
BEGIN
    -- Build update data
    update_data := jsonb_build_object('status', new_status, 'updated_at', NOW());
    
    IF provider_tx_id IS NOT NULL THEN
        update_data := update_data || jsonb_build_object('provider_transaction_id', provider_tx_id);
    END IF;
    
    IF commission_amt IS NOT NULL THEN
        update_data := update_data || jsonb_build_object('commission_amount', commission_amt);
    END IF;
    
    IF failure_reason_val IS NOT NULL THEN
        update_data := update_data || jsonb_build_object('failure_reason', failure_reason_val);
    END IF;
    
    -- Set completion timestamp for completed transactions
    IF new_status = 'completed' THEN
        update_data := update_data || jsonb_build_object('completed_at', NOW());
    END IF;
    
    -- Update the transaction
    UPDATE transactions 
    SET 
        status = (update_data->>'status')::VARCHAR(20),
        updated_at = (update_data->>'updated_at')::TIMESTAMP WITH TIME ZONE,
        provider_transaction_id = COALESCE((update_data->>'provider_transaction_id')::VARCHAR(100), provider_transaction_id),
        commission_amount = COALESCE((update_data->>'commission_amount')::DECIMAL(15,4), commission_amount),
        failure_reason = COALESCE((update_data->>'failure_reason')::VARCHAR(100), failure_reason),
        completed_at = CASE 
            WHEN new_status = 'completed' THEN (update_data->>'completed_at')::TIMESTAMP WITH TIME ZONE
            ELSE completed_at 
        END
    WHERE id = transaction_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Trigger to automatically calculate total_amount when commission is set
CREATE OR REPLACE FUNCTION calculate_total_amount()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.commission_amount IS NOT NULL AND NEW.commission_amount > 0 THEN
        NEW.total_amount := NEW.amount - NEW.commission_amount;
    ELSE
        NEW.total_amount := NEW.amount;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger if it doesn't exist
DROP TRIGGER IF EXISTS trigger_calculate_total_amount ON transactions;
CREATE TRIGGER trigger_calculate_total_amount
    BEFORE INSERT OR UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION calculate_total_amount();

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE vtpass_transaction_logs IS 'Detailed logging of all VTpass API interactions for debugging and audit purposes';
COMMENT ON FUNCTION get_vtpass_stats IS 'Returns comprehensive statistics for VTpass transactions within a date range';
COMMENT ON FUNCTION get_pending_vtpass_transactions IS 'Returns pending transactions that are eligible for retry based on retry count and delay';
COMMENT ON FUNCTION update_vtpass_transaction_status IS 'Helper function to update transaction status with automatic timestamp and related fields';
