<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA Updates - <PERSON><PERSON><PERSON></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            color: #334155;
        }

        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 260px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .sidebar-menu {
            flex: 1;
            list-style: none;
            padding: 20px 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover,
        .sidebar-menu li.active a {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu i {
            width: 20px;
            margin-right: 12px;
        }

        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logout-btn {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .logout-btn i {
            margin-right: 8px;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .content-header h1 {
            font-size: 2rem;
            font-weight: 600;
            color: #1e293b;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        /* Buttons */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        .btn-icon {
            padding: 8px;
            background: transparent;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            color: #64748b;
            transition: all 0.3s ease;
        }

        .btn-icon:hover {
            background: #f1f5f9;
            color: #334155;
        }

        /* Content Sections */
        .content-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .section-header {
            padding: 25px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
        }

        /* Tables */
        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #475569;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        .loading-row,
        .no-data {
            text-align: center;
            color: #64748b;
            font-style: italic;
        }

        /* Badges */
        .platform-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .platform-android {
            background: #dcfce7;
            color: #166534;
        }

        .platform-ios {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fee2e2;
            color: #dc2626;
        }

        .mandatory-badge {
            padding: 2px 6px;
            background: #fef3c7;
            color: #d97706;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-left: 8px;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 30px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-header h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #64748b;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .file-upload {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload:hover {
            border-color: #667eea;
            background: #f8fafc;
        }

        .file-upload.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .file-upload input {
            display: none;
        }

        .upload-progress {
            margin-top: 20px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
            }

            .main-content {
                padding: 20px;
            }

            .content-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .modal-content {
                padding: 20px;
                margin: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-mobile-alt"></i> Vendy Admin</h2>
            </div>
            <ul class="sidebar-menu">
                <li>
                    <a href="/dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="active">
                    <a href="/ota-updates">
                        <i class="fas fa-download"></i>
                        <span>OTA Updates</span>
                    </a>
                </li>
                <li>
                    <a href="/analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                </li>
                <li>
                    <a href="/settings">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
            </ul>
            <div class="sidebar-footer">
                <button onclick="logout()" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <h1>OTA Updates Management</h1>
                <div class="header-actions">
                    <button class="btn btn-outline" onclick="refreshUpdates()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <button class="btn btn-primary" onclick="showModal('deployModal')">
                        <i class="fas fa-plus"></i> Deploy Update
                    </button>
                </div>
            </header>

            <!-- Updates Table -->
            <div class="content-section">
                <div class="section-header">
                    <h2>All Updates</h2>
                    <div class="header-actions">
                        <select id="platformFilter" onchange="filterUpdates()">
                            <option value="">All Platforms</option>
                            <option value="android">Android</option>
                            <option value="ios">iOS</option>
                        </select>
                        <select id="statusFilter" onchange="filterUpdates()">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Version</th>
                                <th>Platform</th>
                                <th>Status</th>
                                <th>Size</th>
                                <th>Created</th>
                                <th>Description</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="updatesTable">
                            <tr>
                                <td colspan="7" class="loading-row">
                                    <i class="fas fa-spinner fa-spin"></i> Loading updates...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- Deploy Update Modal -->
    <div id="deployModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Deploy New Update</h3>
                <button class="modal-close" onclick="hideModal('deployModal')">&times;</button>
            </div>
            <form id="deployForm">
                <div class="form-group">
                    <label for="bundleFile">Update Bundle</label>
                    <div class="file-upload" onclick="document.getElementById('bundleFile').click()">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: #667eea; margin-bottom: 10px;"></i>
                        <p>Click to select bundle file or drag and drop</p>
                        <small>Supported formats: ZIP files up to 100MB</small>
                        <input type="file" id="bundleFile" accept=".zip" required>
                    </div>
                    <div class="upload-progress" id="uploadProgress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <p id="progressText">0%</p>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div class="form-group">
                        <label for="version">Version</label>
                        <input type="text" id="version" name="version" placeholder="1.0.2" required>
                    </div>
                    <div class="form-group">
                        <label for="buildNumber">Build Number</label>
                        <input type="text" id="buildNumber" name="buildNumber" placeholder="3" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="platform">Platform</label>
                    <select id="platform" name="platform" required>
                        <option value="">Select Platform</option>
                        <option value="android">Android</option>
                        <option value="ios">iOS</option>
                        <option value="both">Both</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <input type="text" id="description" name="description" placeholder="Bug fixes and improvements" required>
                </div>

                <div class="form-group">
                    <label for="releaseNotes">Release Notes</label>
                    <textarea id="releaseNotes" name="releaseNotes" placeholder="- Fixed login issue&#10;- Improved performance&#10;- Updated UI"></textarea>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div class="form-group">
                        <label for="minAppVersion">Min App Version (Optional)</label>
                        <input type="text" id="minAppVersion" name="minAppVersion" placeholder="1.0.0">
                    </div>
                    <div class="form-group">
                        <label for="maxAppVersion">Max App Version (Optional)</label>
                        <input type="text" id="maxAppVersion" name="maxAppVersion" placeholder="2.0.0">
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="isMandatory" name="isMandatory">
                        <label for="isMandatory">Mandatory Update (Forces users to update)</label>
                    </div>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 30px;">
                    <button type="button" class="btn btn-outline" onclick="hideModal('deployModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="deployBtn">
                        <i class="fas fa-rocket"></i> Deploy Update
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="/js/dashboard.js"></script>
    <script>
        let allUpdates = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUpdates();
            setupFileUpload();
        });

        async function loadUpdates() {
            try {
                const response = await fetch('/api/admin/ota/updates');
                if (!response.ok) {
                    if (response.status === 401) {
                        window.location.href = '/login';
                        return;
                    }
                    throw new Error('Failed to load updates');
                }

                const data = await response.json();
                if (data.success) {
                    allUpdates = data.data.updates;
                    displayUpdates(allUpdates);
                }
            } catch (error) {
                console.error('Error loading updates:', error);
                showError('Failed to load updates');
            }
        }

        function displayUpdates(updates) {
            const tbody = document.getElementById('updatesTable');
            
            if (updates.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="no-data">No updates found</td></tr>';
                return;
            }

            tbody.innerHTML = updates.map(update => `
                <tr>
                    <td>
                        <strong>${update.version}</strong>
                        <small>(${update.buildNumber})</small>
                    </td>
                    <td>
                        <span class="platform-badge platform-${update.platform}">
                            ${update.platform}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge ${update.isActive ? 'status-active' : 'status-inactive'}">
                            ${update.isActive ? 'Active' : 'Inactive'}
                        </span>
                        ${update.isMandatory ? '<span class="mandatory-badge">Mandatory</span>' : ''}
                    </td>
                    <td>${formatBytes(update.size)}</td>
                    <td>${formatDate(update.createdAt)}</td>
                    <td title="${update.description}">${update.description.substring(0, 50)}${update.description.length > 50 ? '...' : ''}</td>
                    <td>
                        <button class="btn-icon" onclick="toggleUpdate('${update.id}', ${!update.isActive})" 
                                title="${update.isActive ? 'Deactivate' : 'Activate'}">
                            <i class="fas fa-${update.isActive ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="btn-icon" onclick="viewUpdate('${update.id}')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon" onclick="deleteUpdate('${update.id}')" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function filterUpdates() {
            const platformFilter = document.getElementById('platformFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            let filtered = allUpdates;

            if (platformFilter) {
                filtered = filtered.filter(u => u.platform === platformFilter);
            }

            if (statusFilter) {
                filtered = filtered.filter(u => 
                    statusFilter === 'active' ? u.isActive : !u.isActive
                );
            }

            displayUpdates(filtered);
        }

        async function toggleUpdate(updateId, activate) {
            try {
                const response = await fetch(`/api/admin/ota/updates/${updateId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ isActive: activate })
                });

                if (response.ok) {
                    showSuccess(`Update ${activate ? 'activated' : 'deactivated'} successfully`);
                    loadUpdates();
                } else {
                    throw new Error('Failed to update');
                }
            } catch (error) {
                showError('Failed to update status');
            }
        }

        function viewUpdate(updateId) {
            const update = allUpdates.find(u => u.id === updateId);
            if (update) {
                alert(`Update Details:\n\nVersion: ${update.version} (${update.buildNumber})\nPlatform: ${update.platform}\nDescription: ${update.description}\nRelease Notes: ${update.releaseNotes || 'None'}\nSize: ${formatBytes(update.size)}\nCreated: ${formatDate(update.createdAt)}\nMandatory: ${update.isMandatory ? 'Yes' : 'No'}\nActive: ${update.isActive ? 'Yes' : 'No'}`);
            }
        }

        async function deleteUpdate(updateId) {
            if (!confirm('Are you sure you want to delete this update? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/ota/updates/${updateId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    showSuccess('Update deleted successfully');
                    loadUpdates();
                } else {
                    throw new Error('Failed to delete');
                }
            } catch (error) {
                showError('Failed to delete update');
            }
        }

        function setupFileUpload() {
            const fileUpload = document.querySelector('.file-upload');
            const fileInput = document.getElementById('bundleFile');

            // Drag and drop
            fileUpload.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUpload.classList.add('dragover');
            });

            fileUpload.addEventListener('dragleave', () => {
                fileUpload.classList.remove('dragover');
            });

            fileUpload.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUpload.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    updateFileUploadDisplay(files[0]);
                }
            });

            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    updateFileUploadDisplay(e.target.files[0]);
                }
            });
        }

        function updateFileUploadDisplay(file) {
            const fileUpload = document.querySelector('.file-upload');
            fileUpload.innerHTML = `
                <i class="fas fa-file-archive" style="font-size: 2rem; color: #10b981; margin-bottom: 10px;"></i>
                <p><strong>${file.name}</strong></p>
                <small>${formatBytes(file.size)}</small>
            `;
        }

        // Deploy form submission
        document.getElementById('deployForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const deployBtn = document.getElementById('deployBtn');
            const progressDiv = document.getElementById('uploadProgress');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            setLoading(deployBtn, true);
            progressDiv.style.display = 'block';

            try {
                const formData = new FormData();
                const fileInput = document.getElementById('bundleFile');
                
                if (!fileInput.files[0]) {
                    throw new Error('Please select a bundle file');
                }

                formData.append('bundle', fileInput.files[0]);
                formData.append('version', document.getElementById('version').value);
                formData.append('buildNumber', document.getElementById('buildNumber').value);
                formData.append('platform', document.getElementById('platform').value);
                formData.append('description', document.getElementById('description').value);
                formData.append('releaseNotes', document.getElementById('releaseNotes').value);
                formData.append('isMandatory', document.getElementById('isMandatory').checked);
                
                const minVersion = document.getElementById('minAppVersion').value;
                const maxVersion = document.getElementById('maxAppVersion').value;
                if (minVersion) formData.append('minAppVersion', minVersion);
                if (maxVersion) formData.append('maxAppVersion', maxVersion);

                const response = await uploadFile('/api/admin/ota/updates', formData, (progress) => {
                    progressFill.style.width = progress + '%';
                    progressText.textContent = Math.round(progress) + '%';
                });

                if (response.success) {
                    showSuccess('Update deployed successfully!');
                    hideModal('deployModal');
                    document.getElementById('deployForm').reset();
                    loadUpdates();
                } else {
                    throw new Error(response.error || 'Deployment failed');
                }
            } catch (error) {
                console.error('Deployment error:', error);
                showError(error.message || 'Failed to deploy update');
            } finally {
                setLoading(deployBtn, false);
                progressDiv.style.display = 'none';
                progressFill.style.width = '0%';
                progressText.textContent = '0%';
            }
        });

        function refreshUpdates() {
            loadUpdates();
            showSuccess('Updates refreshed');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('/logout', { method: 'POST' })
                    .then(() => {
                        window.location.href = '/login';
                    });
            }
        }
    </script>
</body>
</html>
