const mongoose = require('mongoose');

/**
 * OTA Update Schema
 * Manages OTA update configurations and metadata
 */
const otaUpdateSchema = new mongoose.Schema({
  // Update Identification
  id: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // Version Information
  version: {
    type: String,
    required: true,
    index: true
  },
  buildNumber: {
    type: String,
    required: true
  },
  
  // Platform and Compatibility
  platform: {
    type: String,
    required: true,
    enum: ['ios', 'android', 'both'],
    index: true
  },
  minAppVersion: String,
  maxAppVersion: String,
  
  // File Information
  filename: {
    type: String,
    required: true
  },
  downloadUrl: {
    type: String,
    required: true
  },
  checksum: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true
  },
  
  // Update Configuration
  isMandatory: {
    type: Boolean,
    default: false,
    index: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  
  // Content
  description: {
    type: String,
    required: true
  },
  releaseNotes: String,
  
  // Rollout Configuration
  rollout: {
    strategy: {
      type: String,
      enum: ['immediate', 'percentage', 'staged', 'targeted'],
      default: 'immediate'
    },
    percentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 100
    },
    targetUsers: [String],
    excludedUsers: [String],
    rolloutStartDate: Date,
    rolloutEndDate: Date,
    stages: [{
      percentage: Number,
      startDate: Date,
      duration: Number // in hours
    }]
  },
  
  // A/B Testing
  abTest: {
    enabled: {
      type: Boolean,
      default: false
    },
    testId: String,
    variant: String,
    controlGroup: {
      type: Number,
      min: 0,
      max: 100,
      default: 50
    }
  },
  
  // Rollback Configuration
  rollback: {
    enabled: {
      type: Boolean,
      default: true
    },
    previousVersion: String,
    rollbackThreshold: {
      errorRate: {
        type: Number,
        default: 5 // percentage
      },
      crashRate: {
        type: Number,
        default: 2 // percentage
      }
    },
    autoRollback: {
      type: Boolean,
      default: false
    }
  },
  
  // Statistics (cached for performance)
  stats: {
    totalDownloads: {
      type: Number,
      default: 0
    },
    successfulInstalls: {
      type: Number,
      default: 0
    },
    failedInstalls: {
      type: Number,
      default: 0
    },
    rollbacks: {
      type: Number,
      default: 0
    },
    lastUpdated: Date
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  deployedAt: Date,
  deactivatedAt: Date,
  
  // Admin Information
  createdBy: {
    type: String,
    required: true
  },
  modifiedBy: String,
  modifiedAt: Date
}, {
  timestamps: true,
  collection: 'ota_updates'
});

// Indexes for performance
otaUpdateSchema.index({ platform: 1, isActive: 1, version: -1 });
otaUpdateSchema.index({ createdAt: -1 });
otaUpdateSchema.index({ 'rollout.rolloutStartDate': 1, 'rollout.rolloutEndDate': 1 });

/**
 * Instance methods
 */
otaUpdateSchema.methods.isEligibleForUser = function(userId, deviceId, userGroup = 'default') {
  // Check if update is active
  if (!this.isActive) return false;
  
  // Check rollout dates
  const now = new Date();
  if (this.rollout.rolloutStartDate && now < this.rollout.rolloutStartDate) return false;
  if (this.rollout.rolloutEndDate && now > this.rollout.rolloutEndDate) return false;
  
  // Check excluded users
  if (this.rollout.excludedUsers.includes(userId)) return false;
  
  // Check targeted users (if specified, only these users get the update)
  if (this.rollout.targetUsers.length > 0 && !this.rollout.targetUsers.includes(userId)) return false;
  
  // Check rollout percentage
  if (this.rollout.strategy === 'percentage' && this.rollout.percentage < 100) {
    const hash = Math.abs((userId || deviceId || '').split('').reduce((a, c) => a + c.charCodeAt(0), 0)) % 100;
    if (hash >= this.rollout.percentage) return false;
  }
  
  // Check staged rollout
  if (this.rollout.strategy === 'staged') {
    const currentStage = this.getCurrentStage();
    if (!currentStage) return false;
    
    const hash = Math.abs((userId || deviceId || '').split('').reduce((a, c) => a + c.charCodeAt(0), 0)) % 100;
    if (hash >= currentStage.percentage) return false;
  }
  
  return true;
};

otaUpdateSchema.methods.getCurrentStage = function() {
  if (this.rollout.strategy !== 'staged' || !this.rollout.stages.length) return null;
  
  const now = new Date();
  
  for (const stage of this.rollout.stages) {
    const stageEnd = new Date(stage.startDate.getTime() + stage.duration * 60 * 60 * 1000);
    if (now >= stage.startDate && now <= stageEnd) {
      return stage;
    }
  }
  
  return null;
};

otaUpdateSchema.methods.shouldAutoRollback = async function() {
  if (!this.rollback.enabled || !this.rollback.autoRollback) return false;
  
  const OTAAnalytics = require('./OTAAnalytics');
  
  // Get recent analytics for this update
  const recentStats = await OTAAnalytics.getUpdateStats(this.id, 1); // Last 24 hours
  
  let totalInstalls = 0;
  let failedInstalls = 0;
  
  recentStats.forEach(stat => {
    if (stat._id === 'install_completed') totalInstalls += stat.count;
    if (stat._id === 'install_failed') failedInstalls += stat.count;
  });
  
  if (totalInstalls === 0) return false;
  
  const errorRate = (failedInstalls / totalInstalls) * 100;
  return errorRate >= this.rollback.rollbackThreshold.errorRate;
};

/**
 * Static methods
 */
otaUpdateSchema.statics.findApplicableUpdate = async function(currentVersion, currentBuildNumber, platform, userId, deviceId) {
  const updates = await this.find({
    platform: { $in: [platform, 'both'] },
    isActive: true
  }).sort({ createdAt: -1 });
  
  for (const update of updates) {
    // Check if this is a newer version
    const isNewer = this.compareVersions(update.version, update.buildNumber, currentVersion, currentBuildNumber);
    if (!isNewer) continue;
    
    // Check version compatibility
    if (update.minAppVersion && this.compareVersions(currentVersion, '0', update.minAppVersion, '0') < 0) continue;
    if (update.maxAppVersion && this.compareVersions(currentVersion, '0', update.maxAppVersion, '0') > 0) continue;
    
    // Check user eligibility
    if (!update.isEligibleForUser(userId, deviceId)) continue;
    
    return update;
  }
  
  return null;
};

otaUpdateSchema.statics.compareVersions = function(version1, build1, version2, build2) {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);
  
  // Compare version parts
  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;
    
    if (v1Part > v2Part) return 1;
    if (v1Part < v2Part) return -1;
  }
  
  // If versions are equal, compare build numbers
  const b1 = parseInt(build1) || 0;
  const b2 = parseInt(build2) || 0;
  
  if (b1 > b2) return 1;
  if (b1 < b2) return -1;
  
  return 0;
};

module.exports = mongoose.model('OTAUpdate', otaUpdateSchema);
