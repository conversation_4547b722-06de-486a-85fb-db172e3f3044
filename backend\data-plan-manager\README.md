# Data Plan Manager

A comprehensive data plan management system for handling multiple vendor data plans with automatic price updates and real-time synchronization.

## Features

- **Multi-Vendor Support**: Currently supports PluginNG with easy extensibility for additional vendors
- **Automatic Price Updates**: Scheduled updates every 6 hours with price change detection
- **Real-time Updates**: WebSocket-based real-time updates to frontend without server restart
- **Intelligent Caching**: Multi-level caching for optimal performance
- **Price Change Detection**: Automatic detection and notification of price changes
- **Admin Management**: Comprehensive admin interface for managing plans and vendors
- **JSON-based Storage**: Fast file-based storage for better performance than database queries

## Architecture

```
data-plan-manager/
├── config/
│   └── pluginng.config.js      # PluginNG vendor configuration
├── services/
│   ├── dataPlanManager.js      # Main manager service
│   ├── pluginngDataPlanService.js  # PluginNG specific service
│   └── realTimeUpdateService.js    # WebSocket real-time updates
├── routes/
│   └── dataPlanRoutes.js       # API routes
├── data/
│   ├── vendors/
│   │   └── pluginng/
│   │       ├── plans.json      # Current plans
│   │       ├── metadata.json   # Update metadata
│   │       └── price-history.json  # Price change history
│   └── compiled-plans.json     # Compiled plans from all vendors
└── index.js                    # Main entry point
```

## Installation

1. **Install Dependencies**
   ```bash
   cd backend
   npm install node-cron ws
   ```

2. **Environment Configuration**
   ```bash
   cp data-plan-manager/.env.example data-plan-manager/.env
   # Edit .env with your PluginNG credentials
   ```

3. **Integration with Main Backend**
   Add to your main `server.js`:
   ```javascript
   const dataPlanManagerModule = require('./data-plan-manager');
   
   // After creating your Express app and HTTP server
   await dataPlanManagerModule.initialize(app, server);
   ```

## API Endpoints

### Public Endpoints

- `GET /api/data-plans` - Get all data plans
- `GET /api/data-plans/network/:network` - Get plans for specific network
- `GET /api/data-plans/cheapest/:network/:size` - Get cheapest plan
- `GET /api/data-plans/status` - Get system status

### Admin Endpoints

- `POST /api/data-plans/admin/force-update` - Force update all vendors
- `POST /api/data-plans/admin/force-update/:vendor` - Force update specific vendor
- `DELETE /api/data-plans/admin/cache` - Clear all cache
- `GET /api/data-plans/admin/vendors` - Get vendor information
- `GET /api/data-plans/admin/realtime-clients` - Get WebSocket client info

## WebSocket Real-time Updates

Connect to `/ws/data-plans` for real-time updates:

```javascript
const ws = new WebSocket('ws://localhost:3000/ws/data-plans');

ws.onopen = () => {
  // Set client type
  ws.send(JSON.stringify({
    type: 'set_client_type',
    clientType: 'admin' // or 'user'
  }));
  
  // Subscribe to specific vendors
  ws.send(JSON.stringify({
    type: 'subscribe_to_vendor',
    vendors: ['pluginng']
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  switch(data.type) {
    case 'plans_updated':
      console.log('Plans updated:', data);
      // Refresh your UI
      break;
    case 'price_changes':
      console.log('Price changes detected:', data);
      // Show admin notification
      break;
  }
};
```

## Data Structure

### Plan Data Format
```json
{
  "vendor": {
    "id": "pluginng",
    "name": "PluginNG",
    "lastUpdated": "2025-01-16T10:30:00Z",
    "totalPlans": 45
  },
  "networks": {
    "mtn_sme": {
      "network": "mtn",
      "type": "sme",
      "displayName": "MTN SME",
      "subcategoryId": 1,
      "status": "active",
      "plans": [
        {
          "planId": "pluginng_1_1gb_for_30_days",
          "name": "1GB for 30 days",
          "size": "1GB",
          "validity": "30 days",
          "type": "sme",
          "vendorPrice": 260,
          "markup": 39,
          "sellingPrice": 300,
          "profitMargin": 15,
          "subcategoryId": 1,
          "status": "active",
          "lastUpdated": "2025-01-16T10:30:00Z"
        }
      ]
    }
  }
}
```

## Adding New Vendors

1. **Create Vendor Configuration**
   ```javascript
   // config/newvendor.config.js
   class NewVendorConfig {
     constructor() {
       this.vendorId = 'newvendor';
       this.vendorName = 'New Vendor';
       // ... configuration
     }
   }
   ```

2. **Create Vendor Service**
   ```javascript
   // services/newvendorDataPlanService.js
   class NewVendorDataPlanService extends EventEmitter {
     async fetchDataPlans() {
       // Implement vendor-specific fetching
     }
     
     parseDataPlans(rawPlans) {
       // Parse to standard format
     }
   }
   ```

3. **Register in Data Plan Manager**
   ```javascript
   // In dataPlanManager.js registerVendors()
   this.vendors.set('newvendor', {
     id: 'newvendor',
     name: 'New Vendor',
     service: newVendorDataPlanService,
     // ... vendor info
   });
   ```

## Monitoring and Logging

The system provides comprehensive logging for:
- Plan updates and changes
- Price change detection
- WebSocket connections
- API requests
- Error handling

All logs include structured data for easy monitoring and debugging.

## Performance Considerations

- **Caching**: Multi-level caching reduces API calls and improves response times
- **Background Updates**: Scheduled updates run in background without affecting user experience
- **Efficient Storage**: JSON files provide faster read access than database queries
- **Real-time Updates**: WebSocket connections eliminate need for polling

## Security

- Admin endpoints require authentication (implement `requireAdmin` middleware)
- Input validation on all API endpoints
- Rate limiting on vendor API calls
- Secure credential storage in environment variables

## Troubleshooting

### Common Issues

1. **PluginNG Authentication Fails**
   - Check credentials in `.env` file
   - Verify PluginNG API is accessible
   - Check token expiry handling

2. **Plans Not Updating**
   - Check scheduled job configuration
   - Verify vendor API endpoints
   - Check logs for error messages

3. **WebSocket Connections Failing**
   - Verify server supports WebSocket upgrades
   - Check firewall settings
   - Monitor connection heartbeat

### Debug Mode

Enable debug logging:
```javascript
// Set LOG_LEVEL=debug in environment
process.env.LOG_LEVEL = 'debug';
```

## Future Enhancements

- [ ] Add more vendor integrations
- [ ] Implement web scraping for vendors without APIs
- [ ] Add price prediction algorithms
- [ ] Implement bulk plan management
- [ ] Add plan comparison features
- [ ] Create mobile app integration
