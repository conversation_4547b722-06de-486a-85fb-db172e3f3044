/**
 * Production Cache Manager
 *
 * Enterprise-grade caching system using Redis for high-performance
 * Supports 1M+ users with clustering and persistence
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// Load environment variables from data-plan-manager folder
require('dotenv').config({ path: require('path').join(__dirname, '../.env') });

let Redis;
try {
  Redis = require('ioredis');
} catch (error) {
  console.warn('⚠️ Redis (ioredis) not available, using fallback cache');
}
const logger = require('../../utils/logger');

class CacheManager {
  constructor() {
    this.redis = null;
    this.fallbackCache = new Map(); // Fallback for Redis failures
    this.isRedisConnected = false;
    
    // Cache configuration
    this.config = {
      // TTL settings (in seconds)
      ttl: {
        plans: 300, // 5 minutes for data plans
        networks: 600, // 10 minutes for network-specific plans
        cheapest: 180, // 3 minutes for cheapest plans
        status: 60, // 1 minute for status data
        metadata: 1800 // 30 minutes for metadata
      },
      
      // Key prefixes for organization
      prefixes: {
        plans: 'dpm:plans:',
        networks: 'dpm:networks:',
        cheapest: 'dpm:cheapest:',
        status: 'dpm:status:',
        metadata: 'dpm:metadata:',
        locks: 'dpm:locks:'
      },
      
      // Performance settings
      maxMemoryPolicy: 'allkeys-lru',
      maxConnections: 100,
      retryDelayOnFailover: 100,
      enableOfflineQueue: true
    };
    
    this.initializeRedis();
  }

  /**
   * Initialize Redis connection
   */
  async initializeRedis() {
    try {
      // Parse Redis host (Redis Cloud sometimes includes port in hostname)
      let redisHost = process.env.REDIS_HOST || 'localhost';
      let redisPort = parseInt(process.env.REDIS_PORT) || 6379;

      // Handle Redis Cloud format: hostname:port
      if (redisHost.includes(':')) {
        const [host, port] = redisHost.split(':');
        redisHost = host;
        redisPort = parseInt(port) || redisPort;
      }

      // Detect if using Redis Cloud
      const isRedisCloud = redisHost &&
                          !redisHost.includes('localhost') &&
                          !redisHost.includes('127.0.0.1');

      // Redis configuration for production/cloud
      const redisConfig = {
        host: redisHost,
        port: redisPort,
        password: process.env.REDIS_PASSWORD || undefined,
        db: parseInt(process.env.REDIS_DB) || 0,

        // TLS for Redis Cloud (only if explicitly enabled)
        tls: process.env.REDIS_TLS === 'true' ? {
          rejectUnauthorized: true, // Use proper TLS validation
          servername: redisHost // Ensure proper SNI
        } : undefined,

        // Connection pool settings
        maxRetriesPerRequest: 3,
        retryDelayOnFailover: this.config.retryDelayOnFailover,
        enableOfflineQueue: true, // Enable for Redis Cloud

        // Performance optimizations for cloud
        lazyConnect: false, // Connect immediately for better error detection
        keepAlive: isRedisCloud ? 60000 : 30000, // Longer keepalive for cloud
        connectTimeout: isRedisCloud ? 30000 : 10000, // Longer timeout for cloud
        commandTimeout: isRedisCloud ? 15000 : 5000,

        // Cloud-specific settings
        enableReadyCheck: true,
        maxRetriesPerRequest: isRedisCloud ? 3 : 3, // Reasonable retries
        retryDelayOnClusterDown: 300,
        retryDelayOnFailover: 100,

        // Family preference (IPv4 for better cloud compatibility)
        family: 4,

        // Additional Redis Cloud optimizations
        showFriendlyErrorStack: true,
        enableAutoPipelining: false // Disable for better error handling
      };

      logger.info('🔧 [CACHE_MANAGER] Initializing Redis connection:', {
        host: redisConfig.host,
        port: redisConfig.port,
        isCloud: isRedisCloud,
        tlsEnabled: !!redisConfig.tls,
        db: redisConfig.db
      });

      // Initialize Redis client
      this.redis = new Redis(redisConfig);

      // Event handlers with enhanced logging
      this.redis.on('connect', () => {
        logger.info('✅ [CACHE_MANAGER] Redis connected successfully', {
          host: redisConfig.host,
          port: redisConfig.port,
          isCloud: isRedisCloud
        });
        this.isRedisConnected = true;
      });

      this.redis.on('ready', () => {
        logger.info('✅ [CACHE_MANAGER] Redis ready for operations');
        this.configureRedis();
      });

      this.redis.on('error', (error) => {
        logger.error('❌ [CACHE_MANAGER] Redis connection error:', {
          error: error.message,
          code: error.code,
          host: redisConfig.host,
          fallbackActive: true
        });
        this.isRedisConnected = false;
      });

      this.redis.on('close', () => {
        logger.warn('⚠️ [CACHE_MANAGER] Redis connection closed, using fallback cache');
        this.isRedisConnected = false;
      });

      this.redis.on('reconnecting', (delay) => {
        logger.info('🔄 [CACHE_MANAGER] Redis reconnecting...', {
          delay,
          attempt: this.redis.retryAttempts
        });
      });

      this.redis.on('end', () => {
        logger.warn('🔌 [CACHE_MANAGER] Redis connection ended');
        this.isRedisConnected = false;
      });

      // Test connection with a real operation instead of just ping
      try {
        logger.info('🔗 [CACHE_MANAGER] Testing Redis connection with real operation...');

        // Test with a real SET/GET operation
        const testKey = 'connection:test:' + Date.now();
        const testValue = 'connection_test_value';

        await this.redis.set(testKey, testValue);
        const result = await this.redis.get(testKey);
        await this.redis.del(testKey);

        if (result === testValue) {
          logger.info('✅ [CACHE_MANAGER] Redis connection test successful');
          this.isRedisConnected = true;
        } else {
          throw new Error('SET/GET test failed');
        }

      } catch (connectionError) {
        logger.warn('⚠️ [CACHE_MANAGER] Redis connection test failed, using fallback cache:', {
          error: connectionError.message,
          code: connectionError.code,
          host: redisHost,
          port: redisPort,
          tls: !!redisConfig.tls,
          isCloud: isRedisCloud
        });

        this.isRedisConnected = false;

        // Don't treat this as a fatal error - fallback cache will work
        logger.info('📦 [CACHE_MANAGER] Fallback cache is active and ready');
      }
      
    } catch (error) {
      logger.error('❌ [CACHE_MANAGER] Redis initialization failed:', {
        error: error.message,
        fallbackActive: true
      });
      this.isRedisConnected = false;
    }
  }

  /**
   * Configure Redis for optimal performance
   */
  async configureRedis() {
    try {
      if (!this.isRedisConnected) return;

      // Skip configuration for Redis Cloud (managed service)
      const isRedisCloud = this.redis.options.host &&
                          !this.redis.options.host.includes('localhost') &&
                          !this.redis.options.host.includes('127.0.0.1');

      if (isRedisCloud) {
        logger.info('✅ [CACHE_MANAGER] Redis Cloud detected - using managed configuration');
        return;
      }

      // Set memory policy for self-hosted Redis only
      await this.redis.config('SET', 'maxmemory-policy', this.config.maxMemoryPolicy);

      logger.info('✅ [CACHE_MANAGER] Redis configured for production use');
    } catch (error) {
      logger.warn('⚠️ [CACHE_MANAGER] Redis configuration skipped (managed service):', {
        error: error.message
      });
    }
  }

  /**
   * Set cache value with TTL
   */
  async set(key, value, ttlSeconds = null) {
    const serializedValue = JSON.stringify(value);
    const timestamp = Date.now();
    
    // Add metadata for tracking
    const cacheData = {
      value,
      timestamp,
      ttl: ttlSeconds
    };

    try {
      if (this.isRedisConnected) {
        if (ttlSeconds) {
          await this.redis.setex(key, ttlSeconds, JSON.stringify(cacheData));
        } else {
          await this.redis.set(key, JSON.stringify(cacheData));
        }
        
        logger.debug('📝 [CACHE_MANAGER] Redis cache set:', {
          key: key.substring(0, 50) + '...',
          size: serializedValue.length,
          ttl: ttlSeconds
        });
        
        return true;
      } else {
        // Fallback to in-memory cache
        this.fallbackCache.set(key, {
          data: cacheData,
          expiry: ttlSeconds ? timestamp + (ttlSeconds * 1000) : null
        });
        
        logger.debug('📝 [CACHE_MANAGER] Fallback cache set:', { key });
        return true;
      }
    } catch (error) {
      logger.error('❌ [CACHE_MANAGER] Cache set failed:', {
        key,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Get cache value
   */
  async get(key) {
    try {
      if (this.isRedisConnected) {
        const cached = await this.redis.get(key);
        
        if (cached) {
          const cacheData = JSON.parse(cached);
          
          logger.debug('📖 [CACHE_MANAGER] Redis cache hit:', {
            key: key.substring(0, 50) + '...',
            age: Date.now() - cacheData.timestamp
          });
          
          return cacheData.value;
        }
        
        logger.debug('📖 [CACHE_MANAGER] Redis cache miss:', { key });
        return null;
        
      } else {
        // Fallback to in-memory cache
        const cached = this.fallbackCache.get(key);
        
        if (cached) {
          // Check expiry
          if (cached.expiry && Date.now() > cached.expiry) {
            this.fallbackCache.delete(key);
            return null;
          }
          
          logger.debug('📖 [CACHE_MANAGER] Fallback cache hit:', { key });
          return cached.data.value;
        }
        
        return null;
      }
    } catch (error) {
      logger.error('❌ [CACHE_MANAGER] Cache get failed:', {
        key,
        error: error.message
      });
      return null;
    }
  }

  /**
   * Delete cache key
   */
  async delete(key) {
    try {
      if (this.isRedisConnected) {
        const result = await this.redis.del(key);
        logger.debug('🗑️ [CACHE_MANAGER] Redis cache deleted:', { key, deleted: result > 0 });
        return result > 0;
      } else {
        const deleted = this.fallbackCache.delete(key);
        logger.debug('🗑️ [CACHE_MANAGER] Fallback cache deleted:', { key, deleted });
        return deleted;
      }
    } catch (error) {
      logger.error('❌ [CACHE_MANAGER] Cache delete failed:', {
        key,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Clear cache by pattern
   */
  async clearPattern(pattern) {
    try {
      if (this.isRedisConnected) {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          const result = await this.redis.del(...keys);
          logger.info('🗑️ [CACHE_MANAGER] Redis pattern cleared:', {
            pattern,
            keysDeleted: result
          });
          return result;
        }
        return 0;
      } else {
        // Fallback pattern clearing
        let deleted = 0;
        const regex = new RegExp(pattern.replace('*', '.*'));
        
        for (const [key] of this.fallbackCache) {
          if (regex.test(key)) {
            this.fallbackCache.delete(key);
            deleted++;
          }
        }
        
        logger.info('🗑️ [CACHE_MANAGER] Fallback pattern cleared:', {
          pattern,
          keysDeleted: deleted
        });
        return deleted;
      }
    } catch (error) {
      logger.error('❌ [CACHE_MANAGER] Pattern clear failed:', {
        pattern,
        error: error.message
      });
      return 0;
    }
  }

  /**
   * Get cache statistics
   */
  async getStats() {
    try {
      if (this.isRedisConnected) {
        const info = await this.redis.info('memory');
        const keyspace = await this.redis.info('keyspace');
        
        return {
          type: 'redis',
          connected: true,
          memory: this.parseRedisInfo(info),
          keyspace: this.parseRedisInfo(keyspace),
          fallbackSize: this.fallbackCache.size
        };
      } else {
        return {
          type: 'fallback',
          connected: false,
          fallbackSize: this.fallbackCache.size,
          memoryUsage: process.memoryUsage()
        };
      }
    } catch (error) {
      logger.error('❌ [CACHE_MANAGER] Stats retrieval failed:', {
        error: error.message
      });
      
      return {
        type: 'error',
        connected: false,
        error: error.message,
        fallbackSize: this.fallbackCache.size
      };
    }
  }

  /**
   * Parse Redis info string
   */
  parseRedisInfo(info) {
    const result = {};
    info.split('\r\n').forEach(line => {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = value;
      }
    });
    return result;
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      // Test if Redis is actually working by doing a real operation
      const testKey = 'health:check:' + Date.now();
      const testValue = 'ping';

      const start = Date.now();

      // Try to set and get a value
      const setResult = await this.set(testKey, testValue, 10);
      if (setResult) {
        const getValue = await this.get(testKey);
        await this.delete(testKey);

        const latency = Date.now() - start;

        if (getValue === testValue) {
          return {
            status: 'healthy',
            type: this.isRedisConnected ? 'redis' : 'fallback',
            latency,
            connected: this.isRedisConnected
          };
        }
      }

      return {
        status: 'degraded',
        type: 'fallback',
        connected: false,
        fallbackActive: true
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        type: 'error',
        connected: false,
        error: error.message
      };
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    try {
      if (this.redis) {
        await this.redis.quit();
        logger.info('✅ [CACHE_MANAGER] Redis connection closed gracefully');
      }
      
      this.fallbackCache.clear();
      logger.info('✅ [CACHE_MANAGER] Cache manager shutdown completed');
    } catch (error) {
      logger.error('❌ [CACHE_MANAGER] Shutdown error:', {
        error: error.message
      });
    }
  }
}

// Create and export singleton instance
const cacheManager = new CacheManager();

module.exports = cacheManager;
