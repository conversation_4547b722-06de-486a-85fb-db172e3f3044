/**
 * Provider Management Controller
 * 
 * Admin controller for managing VTU providers (VTpass, PluginNG)
 * Handles provider switching, configuration, monitoring, and status management
 * 
 * Features:
 * - Manual provider switching
 * - Provider enable/disable
 * - Configuration management
 * - Performance monitoring
 * - Admin action logging
 * - Real-time status updates
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const { body, validationResult } = require('express-validator');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

// Lazy load VTU provider manager to avoid initialization issues
let vtuProviderManager = null;
const getVtuProviderManager = () => {
  if (!vtuProviderManager) {
    vtuProviderManager = require('../services/vtuProviderManager');
  }
  return vtuProviderManager;
};

/**
 * Provider Management Controller Class
 */
class ProviderManagementController {
  constructor() {
    this.supabase = getSupabase();
  }

  /**
   * Get all providers status and configuration
   */
  async getProvidersStatus(req, res) {
    try {
      const { data: providers, error } = await this.supabase
        .from('provider_status')
        .select('*')
        .order('priority', { ascending: true });

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Get real-time provider health from provider manager
      const providersWithHealth = await Promise.all(
        providers.map(async (provider) => {
          try {
            const healthStatus = await this.checkProviderHealth(provider.provider_id);
            return {
              ...provider,
              real_time_health: healthStatus
            };
          } catch (error) {
            return {
              ...provider,
              real_time_health: { status: 'error', message: error.message }
            };
          }
        })
      );

      res.status(200).json({
        success: true,
        data: {
          providers: providersWithHealth,
          active_provider: providersWithHealth.find(p => p.is_primary && p.is_enabled),
          total_providers: providersWithHealth.length,
          enabled_providers: providersWithHealth.filter(p => p.is_enabled).length
        }
      });

    } catch (error) {
      logger.error('❌ [PROVIDER_MANAGEMENT] Get providers status failed:', {
        error: error.message,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to fetch providers status',
        error: error.message
      });
    }
  }

  /**
   * Switch primary provider
   */
  async switchPrimaryProvider(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { provider_id, reason } = req.body;
      const adminUser = req.user;
      const ipAddress = req.ip;
      const userAgent = req.get('User-Agent');

      logger.info('🔄 [PROVIDER_MANAGEMENT] Switching primary provider:', {
        newProvider: provider_id,
        adminId: adminUser.id,
        adminEmail: adminUser.email,
        reason
      });

      // Call database function to switch provider
      const { data, error } = await this.supabase.rpc('switch_primary_provider', {
        new_primary_provider_id: provider_id,
        admin_user_id: adminUser.id,
        admin_email: adminUser.email,
        ip_address: ipAddress,
        user_agent: userAgent
      });

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Update provider manager
      const providerManager = getVtuProviderManager();
      await providerManager.refreshProviderStatus();

      logger.info('✅ [PROVIDER_MANAGEMENT] Primary provider switched successfully:', {
        newProvider: provider_id,
        adminId: adminUser.id
      });

      res.status(200).json({
        success: true,
        message: `Successfully switched primary provider to ${provider_id}`,
        data: {
          new_primary_provider: provider_id,
          switched_by: adminUser.email,
          switched_at: new Date().toISOString(),
          reason
        }
      });

    } catch (error) {
      logger.error('❌ [PROVIDER_MANAGEMENT] Switch primary provider failed:', {
        error: error.message,
        providerId: req.body.provider_id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to switch primary provider',
        error: error.message
      });
    }
  }

  /**
   * Enable or disable a provider
   */
  async toggleProviderStatus(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { provider_id, enabled, reason } = req.body;
      const adminUser = req.user;
      const ipAddress = req.ip;
      const userAgent = req.get('User-Agent');

      logger.info('🔄 [PROVIDER_MANAGEMENT] Toggling provider status:', {
        providerId: provider_id,
        enabled,
        adminId: adminUser.id,
        reason
      });

      // Call database function to toggle provider
      const { data, error } = await this.supabase.rpc('toggle_provider_status', {
        target_provider_id: provider_id,
        enable_provider: enabled,
        admin_user_id: adminUser.id,
        admin_email: adminUser.email,
        reason: reason,
        ip_address: ipAddress,
        user_agent: userAgent
      });

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Update provider manager
      const providerManager = getVtuProviderManager();
      await providerManager.refreshProviderStatus();

      const action = enabled ? 'enabled' : 'disabled';
      logger.info(`✅ [PROVIDER_MANAGEMENT] Provider ${action} successfully:`, {
        providerId: provider_id,
        adminId: adminUser.id
      });

      res.status(200).json({
        success: true,
        message: `Successfully ${action} provider ${provider_id}`,
        data: {
          provider_id,
          enabled,
          action_by: adminUser.email,
          action_at: new Date().toISOString(),
          reason
        }
      });

    } catch (error) {
      logger.error('❌ [PROVIDER_MANAGEMENT] Toggle provider status failed:', {
        error: error.message,
        providerId: req.body.provider_id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to toggle provider status',
        error: error.message
      });
    }
  }

  /**
   * Update provider configuration
   */
  async updateProviderConfig(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { provider_id } = req.params;
      const { config, reason } = req.body;
      const adminUser = req.user;

      logger.info('🔄 [PROVIDER_MANAGEMENT] Updating provider config:', {
        providerId: provider_id,
        adminId: adminUser.id,
        configKeys: Object.keys(config)
      });

      // Get current config
      const { data: currentProvider, error: fetchError } = await this.supabase
        .from('provider_status')
        .select('config')
        .eq('provider_id', provider_id)
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch current config: ${fetchError.message}`);
      }

      // Update provider config
      const { data, error } = await this.supabase
        .from('provider_status')
        .update({
          config: { ...currentProvider.config, ...config },
          updated_at: new Date().toISOString()
        })
        .eq('provider_id', provider_id)
        .select()
        .single();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Log the action
      await this.logAdminAction({
        admin_user_id: adminUser.id,
        admin_email: adminUser.email,
        action_type: 'update_config',
        provider_id,
        action_description: `Updated configuration for ${provider_id}. Reason: ${reason || 'No reason provided'}`,
        old_values: { config: currentProvider.config },
        new_values: { config: data.config },
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      logger.info('✅ [PROVIDER_MANAGEMENT] Provider config updated successfully:', {
        providerId: provider_id,
        adminId: adminUser.id
      });

      res.status(200).json({
        success: true,
        message: `Successfully updated configuration for ${provider_id}`,
        data: {
          provider_id,
          config: data.config,
          updated_by: adminUser.email,
          updated_at: data.updated_at,
          reason
        }
      });

    } catch (error) {
      logger.error('❌ [PROVIDER_MANAGEMENT] Update provider config failed:', {
        error: error.message,
        providerId: req.params.provider_id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to update provider configuration',
        error: error.message
      });
    }
  }

  /**
   * Get provider performance statistics
   */
  async getProviderStats(req, res) {
    try {
      const { provider_id } = req.params;
      const { period = '24h' } = req.query;

      // Calculate date range based on period
      let startDate, endDate = new Date();
      switch (period) {
        case '1h':
          startDate = new Date(Date.now() - 60 * 60 * 1000);
          break;
        case '24h':
          startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
      }

      // Get performance metrics
      const { data: metrics, error: metricsError } = await this.supabase
        .from('provider_performance_metrics')
        .select('*')
        .eq('provider_id', provider_id)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())
        .order('created_at', { ascending: true });

      if (metricsError) {
        throw new Error(`Failed to fetch metrics: ${metricsError.message}`);
      }

      // Get recent transactions
      const { data: transactions, error: transError } = await this.supabase
        .from('transactions')
        .select('id, amount, status, created_at, completed_at, failure_reason')
        .eq('provider_type', provider_id)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false })
        .limit(100);

      if (transError) {
        throw new Error(`Failed to fetch transactions: ${transError.message}`);
      }

      // Calculate summary statistics
      const summary = this.calculateProviderSummary(metrics, transactions);

      res.status(200).json({
        success: true,
        data: {
          provider_id,
          period,
          summary,
          metrics,
          recent_transactions: transactions.slice(0, 10),
          total_transactions: transactions.length
        }
      });

    } catch (error) {
      logger.error('❌ [PROVIDER_MANAGEMENT] Get provider stats failed:', {
        error: error.message,
        providerId: req.params.provider_id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to fetch provider statistics',
        error: error.message
      });
    }
  }

  /**
   * Get admin actions log
   */
  async getAdminActionsLog(req, res) {
    try {
      const { page = 1, limit = 50, provider_id, action_type } = req.query;
      const offset = (page - 1) * limit;

      let query = this.supabase
        .from('provider_admin_actions')
        .select(`
          *,
          users:admin_user_id (
            first_name,
            last_name,
            email
          )
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (provider_id) {
        query = query.eq('provider_id', provider_id);
      }

      if (action_type) {
        query = query.eq('action_type', action_type);
      }

      const { data: actions, error } = await query;

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Get total count
      let countQuery = this.supabase
        .from('provider_admin_actions')
        .select('id', { count: 'exact', head: true });

      if (provider_id) {
        countQuery = countQuery.eq('provider_id', provider_id);
      }

      if (action_type) {
        countQuery = countQuery.eq('action_type', action_type);
      }

      const { count, error: countError } = await countQuery;

      if (countError) {
        throw new Error(`Count error: ${countError.message}`);
      }

      res.status(200).json({
        success: true,
        data: {
          actions,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / limit)
          }
        }
      });

    } catch (error) {
      logger.error('❌ [PROVIDER_MANAGEMENT] Get admin actions log failed:', {
        error: error.message,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to fetch admin actions log',
        error: error.message
      });
    }
  }

  /**
   * Check provider health
   */
  async checkProviderHealth(providerId) {
    try {
      // This would call the actual provider health check
      // For now, return a mock response
      return {
        status: 'healthy',
        response_time: Math.floor(Math.random() * 1000) + 100,
        last_check: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        last_check: new Date().toISOString()
      };
    }
  }

  /**
   * Calculate provider summary statistics
   */
  calculateProviderSummary(metrics, transactions) {
    const totalRequests = metrics.reduce((sum, m) => sum + m.total_requests, 0);
    const successfulRequests = metrics.reduce((sum, m) => sum + m.successful_requests, 0);
    const failedRequests = metrics.reduce((sum, m) => sum + m.failed_requests, 0);
    
    const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0;
    
    const avgResponseTime = metrics.length > 0 
      ? metrics.reduce((sum, m) => sum + m.avg_response_time, 0) / metrics.length 
      : 0;

    const totalAmount = transactions
      .filter(t => t.status === 'completed')
      .reduce((sum, t) => sum + parseFloat(t.amount), 0);

    return {
      total_requests: totalRequests,
      successful_requests: successfulRequests,
      failed_requests: failedRequests,
      success_rate: Math.round(successRate * 100) / 100,
      avg_response_time: Math.round(avgResponseTime),
      total_amount: totalAmount,
      uptime_percentage: successRate // Simplified uptime calculation
    };
  }

  /**
   * Get provider alerts
   */
  async getProviderAlerts(req, res) {
    try {
      const { provider_id, severity, status, page = 1, limit = 50 } = req.query;
      const offset = (page - 1) * limit;

      let query = this.supabase
        .from('provider_alerts')
        .select('*')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (provider_id) {
        query = query.eq('provider_id', provider_id);
      }

      if (severity) {
        query = query.eq('severity', severity);
      }

      if (status) {
        query = query.eq('status', status);
      }

      const { data: alerts, error } = await query;

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Get total count
      let countQuery = this.supabase
        .from('provider_alerts')
        .select('id', { count: 'exact', head: true });

      if (provider_id) {
        countQuery = countQuery.eq('provider_id', provider_id);
      }

      if (severity) {
        countQuery = countQuery.eq('severity', severity);
      }

      if (status) {
        countQuery = countQuery.eq('status', status);
      }

      const { count, error: countError } = await countQuery;

      if (countError) {
        throw new Error(`Count error: ${countError.message}`);
      }

      res.status(200).json({
        success: true,
        data: {
          alerts,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / limit)
          }
        }
      });

    } catch (error) {
      logger.error('❌ [PROVIDER_MANAGEMENT] Get provider alerts failed:', {
        error: error.message,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to fetch provider alerts',
        error: error.message
      });
    }
  }

  /**
   * Acknowledge provider alert
   */
  async acknowledgeAlert(req, res) {
    try {
      const { alert_id } = req.params;
      const adminUser = req.user;

      const { data, error } = await this.supabase
        .from('provider_alerts')
        .update({
          status: 'acknowledged',
          acknowledged_by: adminUser.id,
          acknowledged_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', alert_id)
        .select()
        .single();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      logger.info('✅ [PROVIDER_MANAGEMENT] Alert acknowledged:', {
        alertId: alert_id,
        adminId: adminUser.id
      });

      res.status(200).json({
        success: true,
        message: 'Alert acknowledged successfully',
        data
      });

    } catch (error) {
      logger.error('❌ [PROVIDER_MANAGEMENT] Acknowledge alert failed:', {
        error: error.message,
        alertId: req.params.alert_id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to acknowledge alert',
        error: error.message
      });
    }
  }

  /**
   * Resolve provider alert
   */
  async resolveAlert(req, res) {
    try {
      const { alert_id } = req.params;
      const { resolution } = req.body;
      const adminUser = req.user;

      const { data, error } = await this.supabase
        .from('provider_alerts')
        .update({
          status: 'resolved',
          resolved_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', alert_id)
        .select()
        .single();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Log the resolution action
      await this.logAdminAction({
        admin_user_id: adminUser.id,
        admin_email: adminUser.email,
        action_type: 'resolve_alert',
        provider_id: data.provider_id,
        action_description: `Resolved alert: ${data.title}. Resolution: ${resolution || 'No details provided'}`,
        old_values: { status: 'acknowledged' },
        new_values: { status: 'resolved', resolution },
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      logger.info('✅ [PROVIDER_MANAGEMENT] Alert resolved:', {
        alertId: alert_id,
        adminId: adminUser.id
      });

      res.status(200).json({
        success: true,
        message: 'Alert resolved successfully',
        data
      });

    } catch (error) {
      logger.error('❌ [PROVIDER_MANAGEMENT] Resolve alert failed:', {
        error: error.message,
        alertId: req.params.alert_id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to resolve alert',
        error: error.message
      });
    }
  }

  /**
   * Test provider connection
   */
  async testProviderConnection(req, res) {
    try {
      const { provider_id } = req.params;
      const adminUser = req.user;

      logger.info('🔍 [PROVIDER_MANAGEMENT] Testing provider connection:', {
        providerId: provider_id,
        adminId: adminUser.id
      });

      // This would call the actual provider health check
      // For now, return a mock response
      const testResult = {
        provider_id,
        status: 'healthy',
        response_time: Math.floor(Math.random() * 1000) + 100,
        test_timestamp: new Date().toISOString(),
        details: {
          connectivity: 'ok',
          authentication: 'ok',
          api_version: 'v1'
        }
      };

      // Log the test action
      await this.logAdminAction({
        admin_user_id: adminUser.id,
        admin_email: adminUser.email,
        action_type: 'test_connection',
        provider_id,
        action_description: `Tested connection to ${provider_id}. Status: ${testResult.status}`,
        old_values: {},
        new_values: testResult,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.status(200).json({
        success: true,
        message: 'Provider connection test completed',
        data: testResult
      });

    } catch (error) {
      logger.error('❌ [PROVIDER_MANAGEMENT] Test provider connection failed:', {
        error: error.message,
        providerId: req.params.provider_id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        message: 'Failed to test provider connection',
        error: error.message
      });
    }
  }

  /**
   * Log admin action
   */
  async logAdminAction(actionData) {
    try {
      const { error } = await this.supabase
        .from('provider_admin_actions')
        .insert([actionData]);

      if (error) {
        logger.error('❌ [PROVIDER_MANAGEMENT] Failed to log admin action:', {
          error: error.message,
          actionData
        });
      }
    } catch (error) {
      logger.error('❌ [PROVIDER_MANAGEMENT] Log admin action error:', {
        error: error.message
      });
    }
  }
}

// Create and export singleton instance
const providerManagementController = new ProviderManagementController();

module.exports = providerManagementController;
