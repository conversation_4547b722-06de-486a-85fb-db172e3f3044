/**
 * Data Plan Manager Service
 * 
 * Central service for managing data plans from multiple vendors
 * Handles caching, real-time updates, and vendor coordination
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');

const logger = require('../../utils/logger');
const pluginNGDataPlanService = require('./pluginngDataPlanService');
const cacheManager = require('./cacheManager');

class DataPlanManager extends EventEmitter {
  constructor() {
    super();
    this.vendors = new Map();
    this.isInitialized = false;
    
    // File paths
    this.dataDir = path.join(__dirname, '../data');
    this.cacheFile = path.join(this.dataDir, 'compiled-plans.json');
    
    this.initializeManager();
  }

  /**
   * Initialize the manager
   */
  async initializeManager() {
    try {
      // Ensure data directory exists
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Register vendors
      this.registerVendors();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Load cached data
      await this.loadCachedData();
      
      this.isInitialized = true;
      
      logger.info('✅ [DATA_PLAN_MANAGER] Manager initialized successfully');
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Manager initialization failed:', {
        error: error.message
      });
    }
  }

  /**
   * Register vendor services
   */
  registerVendors() {
    // Register PluginNG
    this.vendors.set('pluginng', {
      id: 'pluginng',
      name: 'PluginNG',
      service: pluginNGDataPlanService,
      status: 'active',
      priority: 1,
      capabilities: ['data', 'airtime'],
      networks: ['mtn', 'airtel', 'glo', '9mobile']
    });
    
    logger.info('✅ [DATA_PLAN_MANAGER] Vendors registered:', {
      totalVendors: this.vendors.size,
      vendors: Array.from(this.vendors.keys())
    });
  }

  /**
   * Set up event listeners for vendor services
   */
  setupEventListeners() {
    // Listen to PluginNG updates
    pluginNGDataPlanService.on('plansUpdated', (data) => {
      this.handleVendorUpdate(data);
    });

    pluginNGDataPlanService.on('priceChanges', (data) => {
      this.handlePriceChanges(data);
    });

    pluginNGDataPlanService.on('updateError', (data) => {
      this.handleVendorError(data);
    });
  }

  /**
   * Handle vendor plan updates
   */
  async handleVendorUpdate(data) {
    try {
      logger.info('📥 [DATA_PLAN_MANAGER] Received vendor update:', {
        vendor: data.vendor,
        updateId: data.updateId,
        hasChanges: data.changes.hasChanges
      });

      // Clear cache for this vendor
      this.clearVendorCache(data.vendor);
      
      // Recompile plans
      await this.compileAllPlans();
      
      // Emit update event for frontend
      this.emit('dataPlansUpdated', {
        vendor: data.vendor,
        updateId: data.updateId,
        changes: data.changes,
        timestamp: data.timestamp
      });

      logger.info('✅ [DATA_PLAN_MANAGER] Vendor update processed successfully');
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Failed to process vendor update:', {
        vendor: data.vendor,
        error: error.message
      });
    }
  }

  /**
   * Handle price changes
   */
  handlePriceChanges(data) {
    logger.info('💰 [DATA_PLAN_MANAGER] Price changes detected:', {
      vendor: data.vendor,
      totalChanges: data.changes.length
    });

    // Emit price change event for admin notifications
    this.emit('priceChangesDetected', {
      vendor: data.vendor,
      changes: data.changes,
      timestamp: data.timestamp
    });
  }

  /**
   * Handle vendor errors
   */
  handleVendorError(data) {
    logger.error('❌ [DATA_PLAN_MANAGER] Vendor error:', {
      vendor: data.vendor,
      error: data.error
    });

    this.emit('vendorError', data);
  }

  /**
   * Get all data plans
   */
  async getAllPlans() {
    try {
      const cacheKey = 'dpm:plans:all';

      // Check Redis cache first
      const cached = await cacheManager.get(cacheKey);
      if (cached) {
        logger.debug('📖 [DATA_PLAN_MANAGER] Cache hit for all plans');
        return cached;
      }

      // Compile fresh data
      const compiledPlans = await this.compileAllPlans();

      // Cache the result with 5-minute TTL
      await cacheManager.set(cacheKey, compiledPlans, 300);

      logger.debug('📝 [DATA_PLAN_MANAGER] All plans cached successfully');
      return compiledPlans;
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Failed to get all plans:', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get plans by network
   */
  async getPlansByNetwork(network) {
    try {
      const cacheKey = `dpm:networks:${network}`;

      // Check Redis cache first
      const cached = await cacheManager.get(cacheKey);
      if (cached) {
        logger.debug('📖 [DATA_PLAN_MANAGER] Cache hit for network plans:', { network });
        return cached;
      }

      const allPlans = await this.getAllPlans();
      const networkPlans = {};

      // Filter plans by network
      Object.keys(allPlans.vendors).forEach(vendorId => {
        const vendor = allPlans.vendors[vendorId];
        const vendorNetworkPlans = {};

        Object.keys(vendor.networks).forEach(networkKey => {
          if (vendor.networks[networkKey].network === network) {
            vendorNetworkPlans[networkKey] = vendor.networks[networkKey];
          }
        });

        if (Object.keys(vendorNetworkPlans).length > 0) {
          networkPlans[vendorId] = {
            ...vendor,
            networks: vendorNetworkPlans
          };
        }
      });

      // Cache the result with 10-minute TTL
      await cacheManager.set(cacheKey, networkPlans, 600);

      logger.debug('📝 [DATA_PLAN_MANAGER] Network plans cached successfully:', { network });
      return networkPlans;
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Failed to get plans by network:', {
        network,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get cheapest plan for specific size and network
   */
  async getCheapestPlan(network, size) {
    try {
      const networkPlans = await this.getPlansByNetwork(network);
      let cheapestPlan = null;
      let cheapestPrice = Infinity;

      Object.keys(networkPlans).forEach(vendorId => {
        const vendor = networkPlans[vendorId];
        
        Object.keys(vendor.networks).forEach(networkKey => {
          const networkData = vendor.networks[networkKey];
          
          networkData.plans.forEach(plan => {
            if (plan.size === size && plan.sellingPrice < cheapestPrice) {
              cheapestPrice = plan.sellingPrice;
              cheapestPlan = {
                ...plan,
                vendor: vendorId,
                vendorName: vendor.name
              };
            }
          });
        });
      });

      return cheapestPlan;
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Failed to get cheapest plan:', {
        network,
        size,
        error: error.message
      });
      return null;
    }
  }

  /**
   * Compile all plans from all vendors
   */
  async compileAllPlans() {
    try {
      const compiledPlans = {
        lastCompiled: new Date().toISOString(),
        totalVendors: 0,
        totalNetworks: 0,
        totalPlans: 0,
        vendors: {}
      };

      // Get plans from each vendor
      for (const [vendorId, vendorInfo] of this.vendors) {
        try {
          const vendorPlans = await vendorInfo.service.getCurrentPlans();
          
          if (vendorPlans) {
            compiledPlans.vendors[vendorId] = {
              ...vendorPlans.vendor,
              status: vendorInfo.status,
              priority: vendorInfo.priority,
              networks: vendorPlans.networks
            };
            
            compiledPlans.totalVendors++;
            compiledPlans.totalNetworks += Object.keys(vendorPlans.networks).length;
            compiledPlans.totalPlans += vendorPlans.vendor.totalPlans || 0;
          }
        } catch (error) {
          logger.error('❌ [DATA_PLAN_MANAGER] Failed to get plans from vendor:', {
            vendorId,
            error: error.message
          });
        }
      }

      // Save compiled plans to file
      await fs.writeFile(this.cacheFile, JSON.stringify(compiledPlans, null, 2));

      logger.info('✅ [DATA_PLAN_MANAGER] Plans compiled successfully:', {
        totalVendors: compiledPlans.totalVendors,
        totalNetworks: compiledPlans.totalNetworks,
        totalPlans: compiledPlans.totalPlans
      });

      return compiledPlans;
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Failed to compile plans:', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Load cached data from file
   */
  async loadCachedData() {
    try {
      const data = await fs.readFile(this.cacheFile, 'utf8');
      const cachedPlans = JSON.parse(data);

      // Cache the loaded data using Redis/fallback cache
      await cacheManager.set('dpm:plans:all', cachedPlans, 300);

      logger.info('✅ [DATA_PLAN_MANAGER] Cached data loaded successfully');
    } catch (error) {
      if (error.code !== 'ENOENT') {
        logger.error('❌ [DATA_PLAN_MANAGER] Failed to load cached data:', {
          error: error.message
        });
      }
    }
  }

  /**
   * Clear vendor cache
   */
  async clearVendorCache(vendorId) {
    try {
      // Clear vendor-specific patterns
      const patterns = [
        `dmp:plans:*`,
        `dmp:networks:*`,
        `dmp:cheapest:*`
      ];

      let totalCleared = 0;
      for (const pattern of patterns) {
        const cleared = await cacheManager.clearPattern(pattern);
        totalCleared += cleared;
      }

      logger.info('🗑️ [DATA_PLAN_MANAGER] Cache cleared for vendor:', {
        vendorId,
        keysCleared: totalCleared
      });
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Failed to clear vendor cache:', {
        vendorId,
        error: error.message
      });
    }
  }

  /**
   * Clear all cache
   */
  async clearAllCache() {
    try {
      const cleared = await cacheManager.clearPattern('dmp:*');
      logger.info('🗑️ [DATA_PLAN_MANAGER] All cache cleared:', { keysCleared: cleared });
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Failed to clear all cache:', {
        error: error.message
      });
    }
  }

  /**
   * Force update all vendors
   */
  async forceUpdateAll() {
    const results = {};
    
    for (const [vendorId, vendorInfo] of this.vendors) {
      try {
        logger.info('🔄 [DATA_PLAN_MANAGER] Force updating vendor:', { vendorId });
        results[vendorId] = await vendorInfo.service.forceUpdate();
      } catch (error) {
        logger.error('❌ [DATA_PLAN_MANAGER] Force update failed for vendor:', {
          vendorId,
          error: error.message
        });
        results[vendorId] = { success: false, error: error.message };
      }
    }
    
    return results;
  }

  /**
   * Get manager status
   */
  async getStatus() {
    try {
      const status = {
        isInitialized: this.isInitialized,
        totalVendors: this.vendors.size,
        cacheSize: this.cache.size,
        vendors: {},
        lastUpdate: this.lastUpdate
      };

      for (const [vendorId, vendorInfo] of this.vendors.entries()) {
        try {
          if (vendorInfo && vendorInfo.service && typeof vendorInfo.service.getMetadata === 'function') {
            const metadata = await vendorInfo.service.getMetadata();
            status.vendors[vendorId] = {
              name: vendorInfo.name,
              status: vendorInfo.status,
              lastUpdate: metadata.lastUpdate,
              totalPlans: metadata.totalPlans
            };
          } else {
            status.vendors[vendorId] = {
              name: vendorInfo.name || vendorId,
              status: vendorInfo.status || 'unknown',
              lastUpdate: vendorInfo.lastUpdated || 'never',
              totalPlans: this.getVendorPlansCount(vendorId)
            };
          }
        } catch (error) {
          status.vendors[vendorId] = {
            name: vendorInfo.name || vendorId,
            status: 'error',
            error: error.message
          };
        }
      }

      return status;
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Error getting status:', error);
      return {
        isInitialized: this.isInitialized,
        totalVendors: 0,
        cacheSize: 0,
        vendors: {},
        error: error.message
      };
    }
  }

  /**
   * Get all plans organized by vendor (API method)
   */
  async getAllPlans() {
    try {
      logger.info('📊 [DATA_PLAN_MANAGER] Getting all plans...');

      // Build plans data structure (skip cache for now to debug)
      const plans = {};

      if (!this.vendors || this.vendors.size === 0) {
        logger.warn('⚠️ [DATA_PLAN_MANAGER] No vendors available');
        return {};
      }

      for (const [vendorId, vendorInfo] of this.vendors.entries()) {
        try {
          plans[vendorId] = {
            id: vendorId,
            name: vendorInfo.name || vendorId,
            status: vendorInfo.status || 'active',
            priority: vendorInfo.priority || 1,
            networks: vendorInfo.plans || {},
            totalPlans: this.getVendorPlansCount(vendorId),
            lastUpdated: vendorInfo.lastUpdated || this.lastUpdate
          };
        } catch (vendorError) {
          logger.error(`❌ [DATA_PLAN_MANAGER] Error processing vendor ${vendorId}:`, vendorError);
          plans[vendorId] = {
            id: vendorId,
            name: vendorId,
            status: 'error',
            priority: 1,
            networks: {},
            totalPlans: 0,
            lastUpdated: new Date().toISOString()
          };
        }
      }

      logger.info(`✅ [DATA_PLAN_MANAGER] Retrieved plans for ${Object.keys(plans).length} vendors`);
      return plans;
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Failed to get all plans:', error);
      // Return empty object as fallback
      return {};
    }
  }

  /**
   * Get plans by network (API method)
   */
  async getPlansByNetwork(network) {
    try {
      const cacheKey = `dmp:network:${network.toLowerCase()}`;
      const cached = await cacheManager.get(cacheKey);
      if (cached) {
        return cached;
      }

      const plans = [];
      for (const [vendorId, vendorInfo] of this.vendors.entries()) {
        const networkPlans = vendorInfo.plans?.[network.toLowerCase()];
        if (Array.isArray(networkPlans)) {
          networkPlans.forEach(plan => {
            plans.push({
              ...plan,
              vendor: vendorId,
              vendorName: vendorInfo.name,
              network: network.toUpperCase()
            });
          });
        }
      }

      // Cache the result
      await cacheManager.set(cacheKey, plans, 300);
      return plans;
    } catch (error) {
      logger.error(`❌ [DATA_PLAN_MANAGER] Failed to get plans for network ${network}:`, error);
      return [];
    }
  }

  /**
   * Get cheapest plan for network and size (API method)
   */
  async getCheapestPlan(network, size) {
    try {
      const plans = await this.getPlansByNetwork(network);
      const filteredPlans = plans.filter(plan =>
        plan.size?.toLowerCase() === size.toLowerCase()
      );

      if (filteredPlans.length === 0) {
        return null;
      }

      // Sort by selling price and return cheapest
      const cheapest = filteredPlans.sort((a, b) =>
        (a.sellingPrice || a.vendorPrice || a.amount) - (b.sellingPrice || b.vendorPrice || b.amount)
      )[0];

      return cheapest;
    } catch (error) {
      logger.error(`❌ [DATA_PLAN_MANAGER] Failed to get cheapest plan:`, error);
      return null;
    }
  }

  /**
   * Get vendors configuration (API method)
   */
  async getVendorsConfig() {
    try {
      const config = {};
      for (const [vendorId, vendorInfo] of this.vendors.entries()) {
        config[vendorId] = {
          id: vendorId,
          name: vendorInfo.name,
          status: vendorInfo.status || 'active',
          priority: vendorInfo.priority || 1,
          apiEndpoint: vendorInfo.apiEndpoint,
          capabilities: vendorInfo.capabilities || [],
          networks: Object.keys(vendorInfo.plans || {}),
          totalPlans: this.getVendorPlansCount(vendorId),
          lastUpdated: vendorInfo.lastUpdated
        };
      }
      return config;
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Failed to get vendors config:', error);
      throw error;
    }
  }

  /**
   * Update vendor configuration (API method)
   */
  async updateVendorConfig(vendorId, config) {
    try {
      logger.info(`🔧 [DATA_PLAN_MANAGER] Updating vendor config: ${vendorId}`);

      const vendorInfo = this.vendors.get(vendorId);
      if (!vendorInfo) {
        throw new Error(`Vendor ${vendorId} not found`);
      }

      // Update vendor configuration
      const updatedInfo = {
        ...vendorInfo,
        ...config,
        lastUpdated: new Date().toISOString()
      };

      this.vendors.set(vendorId, updatedInfo);

      // Clear vendor cache
      await this.clearVendorCache(vendorId);

      logger.info(`✅ [DATA_PLAN_MANAGER] Vendor ${vendorId} config updated`);
      return updatedInfo;
    } catch (error) {
      logger.error(`❌ [DATA_PLAN_MANAGER] Failed to update vendor ${vendorId} config:`, error);
      throw error;
    }
  }

  /**
   * Get vendor plans count (helper method)
   */
  getVendorPlansCount(vendorId) {
    try {
      const vendorInfo = this.vendors.get(vendorId);
      if (!vendorInfo || !vendorInfo.plans) return 0;

      return Object.values(vendorInfo.plans).reduce((sum, networkPlans) =>
        sum + (Array.isArray(networkPlans) ? networkPlans.length : 0), 0
      );
    } catch (error) {
      logger.error(`❌ [DATA_PLAN_MANAGER] Error counting plans for vendor ${vendorId}:`, error);
      return 0;
    }
  }

  /**
   * Clear cache (API method)
   */
  async clearCache() {
    try {
      await this.clearAllCache();
      logger.info('✅ [DATA_PLAN_MANAGER] Cache cleared via API');
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER] Failed to clear cache via API:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
const dataPlanManager = new DataPlanManager();

module.exports = dataPlanManager;
