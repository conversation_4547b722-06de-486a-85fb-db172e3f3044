# Production Deployment Guide

## 🚀 **Scaling for 1 Million Users**

This guide covers deploying the Data Plan Manager for enterprise-scale production with 1M+ users.

## 📋 **Prerequisites**

### **1. Redis Setup (CRITICAL for 1M users)**

**Option A: Redis Cloud (Recommended)**
```bash
# Sign up at https://redis.com/redis-enterprise-cloud/
# Get connection details and update .env:
REDIS_HOST=your-redis-cloud-endpoint.com
REDIS_PORT=12345
REDIS_PASSWORD=your-secure-password
REDIS_DB=0
```

**Option B: Self-hosted Redis Cluster**
```bash
# Install Redis
sudo apt update
sudo apt install redis-server

# Configure for production (/etc/redis/redis.conf)
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000

# Start Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### **2. Install Dependencies**
```bash
cd backend
npm install ioredis
```

### **3. Environment Configuration**
```bash
# Update .env with production values
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-secure-password
NODE_ENV=production
```

## 🏗️ **Architecture for Scale**

### **Single Server (Up to 100K users)**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │────│   Node.js App   │────│   Redis Cache   │
│    (Nginx)      │    │  (Data Plan Mgr) │    │   (Primary)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **Clustered Setup (1M+ users)**
```
                    ┌─────────────────┐
                    │   Load Balancer │
                    │    (Nginx)      │
                    └─────────┬───────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼───────┐    ┌────────▼────────┐    ┌──────▼──────┐
│  Node.js App  │    │  Node.js App    │    │ Node.js App │
│   Instance 1  │    │   Instance 2    │    │ Instance 3  │
└───────┬───────┘    └────────┬────────┘    └──────┬──────┘
        │                     │                     │
        └─────────────────────┼─────────────────────┘
                              │
                    ┌─────────▼───────┐
                    │ Redis Cluster   │
                    │ (3 Master +     │
                    │  3 Replica)     │
                    └─────────────────┘
```

## ⚡ **Performance Optimizations**

### **1. Redis Configuration**
```redis
# /etc/redis/redis.conf
maxmemory 8gb
maxmemory-policy allkeys-lru
tcp-keepalive 300
timeout 0
tcp-backlog 511
databases 16

# Persistence (adjust based on needs)
save 900 1
save 300 10
save 60 10000

# Network
bind 0.0.0.0
protected-mode yes
port 6379
```

### **2. Node.js Clustering**
```javascript
// cluster.js
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
  console.log(`Master ${process.pid} is running`);
  
  // Fork workers
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }
  
  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died`);
    cluster.fork();
  });
} else {
  require('./server.js');
  console.log(`Worker ${process.pid} started`);
}
```

### **3. Nginx Load Balancer**
```nginx
upstream vendy_backend {
    least_conn;
    server 127.0.0.1:3000 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3002 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name api.vendy.com;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;
    
    location /api/data-plans {
        proxy_pass http://vendy_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    location /ws/data-plans {
        proxy_pass http://vendy_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 **Monitoring & Metrics**

### **1. Redis Monitoring**
```bash
# Redis CLI monitoring
redis-cli info memory
redis-cli info stats
redis-cli monitor

# Key metrics to watch:
# - used_memory_human
# - keyspace_hits/keyspace_misses ratio
# - connected_clients
# - ops_per_sec
```

### **2. Application Metrics**
```javascript
// Add to your monitoring
const metrics = {
  cacheHitRate: (hits / (hits + misses)) * 100,
  averageResponseTime: totalTime / requestCount,
  activeConnections: websocketClients.size,
  memoryUsage: process.memoryUsage(),
  redisConnections: redis.status
};
```

## 🔧 **Production Checklist**

### **Before Deployment:**
- [ ] Redis cluster configured and tested
- [ ] Environment variables set for production
- [ ] SSL certificates installed
- [ ] Load balancer configured
- [ ] Monitoring tools setup
- [ ] Backup strategy implemented
- [ ] Security audit completed

### **Performance Targets:**
- [ ] API response time < 100ms (cached)
- [ ] API response time < 500ms (uncached)
- [ ] Cache hit rate > 90%
- [ ] WebSocket connection time < 1s
- [ ] Memory usage < 80% of available
- [ ] CPU usage < 70% under normal load

## 🚨 **Troubleshooting**

### **High Memory Usage**
```bash
# Check Redis memory
redis-cli info memory

# Check Node.js memory
node --max-old-space-size=4096 server.js

# Monitor with htop
htop
```

### **Cache Miss Issues**
```bash
# Check cache statistics
curl http://localhost:3000/api/data-plans/status

# Monitor Redis keys
redis-cli keys "dpm:*"
```

### **Connection Issues**
```bash
# Test Redis connection
redis-cli ping

# Check WebSocket connections
netstat -an | grep :3000
```

## 📈 **Scaling Recommendations**

### **100K Users:**
- 1 Node.js instance
- 1 Redis instance (2GB RAM)
- Basic monitoring

### **500K Users:**
- 3 Node.js instances
- Redis cluster (3 masters, 8GB RAM total)
- Load balancer
- Advanced monitoring

### **1M+ Users:**
- 5+ Node.js instances
- Redis cluster (5+ masters, 16GB+ RAM)
- CDN for static content
- Database read replicas
- Comprehensive monitoring
- Auto-scaling

## 🔒 **Security for Production**

### **Redis Security:**
```redis
# requirepass your-secure-password
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# bind 127.0.0.1 ********
```

### **Application Security:**
- Enable all security middleware
- Use HTTPS only
- Implement rate limiting
- Regular security audits
- Monitor for suspicious activity

## 💡 **Cost Optimization**

### **Redis Cloud Pricing:**
- 100K users: ~$50/month
- 500K users: ~$200/month  
- 1M users: ~$500/month

### **Server Costs:**
- Single server: $50-100/month
- Clustered setup: $200-500/month
- Enterprise setup: $1000+/month

**Total estimated cost for 1M users: $1000-1500/month**

This production setup will handle 1M+ users with high performance and reliability! 🚀
