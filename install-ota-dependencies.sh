#!/bin/bash

# Install OTA Update Dependencies
# This script installs the required dependencies for the custom OTA update system

echo "🚀 Installing OTA Update Dependencies..."

# Install React Native File System
echo "📁 Installing react-native-fs..."
npm install react-native-fs@^2.20.0

# Install React Native Zip Archive (for bundle extraction)
echo "📦 Installing react-native-zip-archive..."
npm install react-native-zip-archive@^7.0.1

# Note: crypto-js is already installed in your project

echo "✅ Dependencies installed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Run 'npx react-native run-android' or 'npx react-native run-ios' to rebuild the app"
echo "2. Configure your backend URL in src/services/codePushService.ts (line 36)"
echo "3. Create app bundles and update the backend/config/app-updates.json file"
echo "4. Test the OTA update functionality"
echo ""
echo "🔧 For iOS, you may need to run 'cd ios && pod install' after installation"
echo ""
echo "📚 Documentation:"
echo "- Backend API endpoints: backend/routes/appUpdates.js"
echo "- Update configuration: backend/config/app-updates.json"
echo "- OTA Service: src/services/codePushService.ts"
echo "- Update UI: src/components/UpdateModal.tsx"
