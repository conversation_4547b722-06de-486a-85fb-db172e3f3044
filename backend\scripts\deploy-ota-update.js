#!/usr/bin/env node

/**
 * OTA Update Deployment Script
 * 
 * This script helps you deploy OTA updates to your mobile app users.
 * It uploads the bundle and configures the update settings.
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

// Configuration
const CONFIG = {
  API_BASE_URL: process.env.API_BASE_URL || 'https://api.payvendy.name.ng',
  ADMIN_TOKEN: process.env.ADMIN_TOKEN || '', // Set this in your environment
};

/**
 * Deploy OTA update
 */
async function deployOTAUpdate(options) {
  const {
    bundlePath,
    version,
    buildNumber,
    platform,
    description,
    releaseNotes,
    isMandatory = false,
    minAppVersion = null,
    maxAppVersion = null
  } = options;

  try {
    console.log('🚀 Starting OTA update deployment...');
    console.log(`📦 Bundle: ${bundlePath}`);
    console.log(`📱 Platform: ${platform}`);
    console.log(`🔢 Version: ${version} (Build ${buildNumber})`);
    console.log(`⚠️ Mandatory: ${isMandatory ? 'Yes' : 'No'}`);
    console.log('');

    // Verify bundle file exists
    if (!fs.existsSync(bundlePath)) {
      throw new Error(`Bundle file not found: ${bundlePath}`);
    }

    // Verify admin token
    if (!CONFIG.ADMIN_TOKEN) {
      throw new Error('ADMIN_TOKEN environment variable is required');
    }

    // Create form data
    const formData = new FormData();
    formData.append('bundle', fs.createReadStream(bundlePath));
    formData.append('version', version);
    formData.append('buildNumber', buildNumber);
    formData.append('platform', platform);
    formData.append('description', description);
    formData.append('releaseNotes', releaseNotes || '');
    formData.append('isMandatory', isMandatory.toString());
    if (minAppVersion) formData.append('minAppVersion', minAppVersion);
    if (maxAppVersion) formData.append('maxAppVersion', maxAppVersion);

    // Upload the update
    console.log('📤 Uploading update bundle...');
    const response = await axios.post(
      `${CONFIG.API_BASE_URL}/api/v1/admin/ota/updates`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${CONFIG.ADMIN_TOKEN}`
        },
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      }
    );

    if (response.data.success) {
      console.log('✅ Update deployed successfully!');
      console.log('📊 Update Details:');
      console.log(`   ID: ${response.data.data.id}`);
      console.log(`   Download URL: ${response.data.data.downloadUrl}`);
      console.log(`   File Size: ${(response.data.data.size / 1024).toFixed(2)} KB`);
      console.log(`   Checksum: ${response.data.data.checksum}`);
      console.log('');
      console.log('🎉 Users will receive this update based on their app settings!');
      
      return response.data.data;
    } else {
      throw new Error(response.data.error || 'Upload failed');
    }

  } catch (error) {
    console.error('❌ Deployment failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

/**
 * List current updates
 */
async function listUpdates() {
  try {
    console.log('📋 Fetching current updates...');
    
    const response = await axios.get(
      `${CONFIG.API_BASE_URL}/api/v1/admin/ota/updates`,
      {
        headers: {
          'Authorization': `Bearer ${CONFIG.ADMIN_TOKEN}`
        }
      }
    );

    if (response.data.success) {
      const updates = response.data.data.updates;
      console.log(`\n📱 Found ${updates.length} updates:\n`);
      
      updates.forEach((update, index) => {
        console.log(`${index + 1}. ${update.id}`);
        console.log(`   Version: ${update.version} (Build ${update.buildNumber})`);
        console.log(`   Platform: ${update.platform}`);
        console.log(`   Status: ${update.isActive ? '🟢 Active' : '🔴 Inactive'}`);
        console.log(`   Mandatory: ${update.isMandatory ? '⚠️ Yes' : '✅ No'}`);
        console.log(`   Size: ${(update.size / 1024).toFixed(2)} KB`);
        console.log(`   Created: ${new Date(update.createdAt).toLocaleString()}`);
        console.log(`   Description: ${update.description}`);
        console.log('');
      });
    }
  } catch (error) {
    console.error('❌ Failed to list updates:', error.response?.data || error.message);
  }
}

/**
 * Command line interface
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (command === 'deploy') {
    // Parse deployment arguments
    const bundlePath = args[1];
    const version = args[2];
    const buildNumber = args[3];
    const platform = args[4];
    const description = args[5] || 'Bug fixes and improvements';
    const isMandatory = args.includes('--mandatory');
    const releaseNotes = args.find(arg => arg.startsWith('--notes='))?.split('=')[1];

    if (!bundlePath || !version || !buildNumber || !platform) {
      console.log('Usage: node deploy-ota-update.js deploy <bundle-path> <version> <build-number> <platform> [description] [--mandatory] [--notes="Release notes"]');
      console.log('');
      console.log('Examples:');
      console.log('  node deploy-ota-update.js deploy ./vendy-v1.0.2-android.zip 1.0.2 3 android "UI improvements"');
      console.log('  node deploy-ota-update.js deploy ./vendy-v1.0.2-ios.zip 1.0.2 3 ios "Bug fixes" --mandatory');
      process.exit(1);
    }

    await deployOTAUpdate({
      bundlePath,
      version,
      buildNumber,
      platform,
      description,
      releaseNotes,
      isMandatory
    });

  } else if (command === 'list') {
    await listUpdates();

  } else {
    console.log('🚀 Vendy OTA Update Deployment Tool');
    console.log('');
    console.log('Commands:');
    console.log('  deploy <bundle> <version> <build> <platform> [description] [--mandatory]');
    console.log('    Deploy a new OTA update');
    console.log('');
    console.log('  list');
    console.log('    List all current updates');
    console.log('');
    console.log('Environment Variables:');
    console.log('  API_BASE_URL - Your API base URL (default: https://api.payvendy.name.ng)');
    console.log('  ADMIN_TOKEN - Your admin authentication token (required)');
    console.log('');
    console.log('Examples:');
    console.log('  ADMIN_TOKEN=your_token node deploy-ota-update.js deploy ./bundle.zip 1.0.2 3 android');
    console.log('  ADMIN_TOKEN=your_token node deploy-ota-update.js list');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  deployOTAUpdate,
  listUpdates
};
