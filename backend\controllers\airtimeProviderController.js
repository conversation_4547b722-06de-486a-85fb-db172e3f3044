/**
 * Airtime Provider Controller
 * 
 * Simple controller for switching between VTpass and PluginNG providers
 * Handles enabling/disabling providers and status management
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const { supabase } = require('../config/database');
const logger = require('../utils/logger');

// Import VTU Provider Manager
let vtuProviderManager = null;
const getVtuProviderManager = () => {
  if (!vtuProviderManager) {
    vtuProviderManager = require('../services/vtuProviderManager');
  }
  return vtuProviderManager;
};

/**
 * Get current provider status
 */
const getProviderStatus = async (req, res) => {
  try {
    const providerManager = getVtuProviderManager();

    // Get all registered providers from VTU Provider Manager
    const allProviders = Array.from(providerManager.providers.values());

    // Get current provider selection
    const currentProvider = providerManager.getCurrentProvider();
    const currentProviderId = currentProvider ? currentProvider.id : null;

    // Format response for frontend
    const providers = {
      vtpass: {
        enabled: false,
        status: 'inactive',
        name: 'VTpass',
        description: 'Primary airtime provider'
      },
      pluginng: {
        enabled: false,
        status: 'inactive',
        name: 'PluginNG',
        description: 'Backup airtime provider'
      }
    };

    // Update with actual provider status
    allProviders.forEach(provider => {
      if (provider.id === 'vtpass') {
        providers.vtpass.enabled = provider.status === 'active';
        providers.vtpass.status = provider.status;
      }
      if (provider.id === 'pluginng') {
        providers.pluginng.enabled = provider.status === 'active';
        providers.pluginng.status = provider.status;
      }
    });

    res.json({
      success: true,
      providers,
      currentProvider: currentProviderId,
      manualControl: providerManager.manualControlEnabled
    });

  } catch (error) {
    logger.error('Error getting provider status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get provider status',
      error: error.message
    });
  }
};

/**
 * Update provider settings
 */
const updateProviderSettings = async (req, res) => {
  try {
    const { providers } = req.body;
    const adminUserId = req.user?.id;

    if (!providers) {
      return res.status(400).json({
        success: false,
        message: 'Provider settings are required'
      });
    }

    const providerManager = getVtuProviderManager();

    // Enable manual control mode
    providerManager.enableManualControl();

    // Update VTpass provider status
    if (providers.vtpass !== undefined) {
      if (providers.vtpass.enabled) {
        providerManager.setProviderStatus('vtpass', 'active');
        logger.info('✅ VTpass provider enabled by admin', { adminUserId });
      } else {
        providerManager.setProviderStatus('vtpass', 'inactive');
        logger.info('❌ VTpass provider disabled by admin', { adminUserId });
      }
    }

    // Update PluginNG provider status
    if (providers.pluginng !== undefined) {
      if (providers.pluginng.enabled) {
        providerManager.setProviderStatus('pluginng', 'active');
        logger.info('✅ PluginNG provider enabled by admin', { adminUserId });
      } else {
        providerManager.setProviderStatus('pluginng', 'inactive');
        logger.info('❌ PluginNG provider disabled by admin', { adminUserId });
      }
    }

    // Set current provider based on what's enabled
    const activeProviders = providerManager.getActiveProviders();
    if (activeProviders.length > 0) {
      // Prefer VTpass if both are enabled
      const preferredProvider = activeProviders.find(p => p.id === 'vtpass') || activeProviders[0];
      providerManager.setCurrentProvider(preferredProvider.id);
      logger.info('🎯 Current provider set to:', { providerId: preferredProvider.id });
    } else {
      logger.warn('⚠️ No active providers available after update');
    }

    // Save to database for persistence
    await providerManager.saveProviderStatusToDatabase();

    // Log admin action
    try {
      await supabase
        .from('admin_actions')
        .insert({
          admin_user_id: adminUserId,
          action_type: 'update_provider_settings',
          target_type: 'airtime_providers',
          action_description: `Updated airtime provider settings: VTpass ${providers.vtpass?.enabled ? 'enabled' : 'disabled'}, PluginNG ${providers.pluginng?.enabled ? 'enabled' : 'disabled'}`,
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        });
    } catch (logError) {
      logger.warn('Failed to log admin action:', logError);
    }

    logger.info('🎉 Provider settings updated successfully', {
      adminUserId,
      vtpass: providers.vtpass?.enabled,
      pluginng: providers.pluginng?.enabled,
      activeProviders: activeProviders.map(p => p.id)
    });

    res.json({
      success: true,
      message: 'Provider settings updated successfully',
      activeProviders: activeProviders.map(p => ({ id: p.id, name: p.name })),
      currentProvider: providerManager.getCurrentProvider()?.id
    });

  } catch (error) {
    logger.error('❌ Error updating provider settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update provider settings',
      error: error.message
    });
  }
};

/**
 * Test provider connection
 */
const testProvider = async (req, res) => {
  try {
    const { provider } = req.params;
    
    if (!['vtpass', 'pluginng'].includes(provider)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid provider specified'
      });
    }

    // Mock test - replace with actual provider test
    const testResult = {
      provider,
      status: 'success',
      response_time: Math.floor(Math.random() * 1000) + 200,
      message: `${provider} connection test successful`
    };

    res.json({
      success: true,
      test_result: testResult
    });

  } catch (error) {
    logger.error('Error testing provider:', error);
    res.status(500).json({
      success: false,
      message: 'Provider test failed',
      error: error.message
    });
  }
};

/**
 * Get provider statistics
 */
const getProviderStats = async (req, res) => {
  try {
    // Mock stats - replace with actual database queries
    const stats = {
      vtpass: {
        total_transactions: 1250,
        success_rate: 98.5,
        avg_response_time: 850,
        last_24h_transactions: 45
      },
      pluginng: {
        total_transactions: 320,
        success_rate: 96.2,
        avg_response_time: 1200,
        last_24h_transactions: 12
      }
    };

    res.json({
      success: true,
      stats
    });

  } catch (error) {
    logger.error('Error getting provider stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get provider statistics',
      error: error.message
    });
  }
};

module.exports = {
  getProviderStatus,
  updateProviderSettings,
  testProvider,
  getProviderStats
};
