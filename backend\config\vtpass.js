/**
 * VTpass API Configuration Module
 * 
 * Centralized configuration for VTpass MTN VTU API integration.
 * Handles environment variables, validation, and security settings.
 * 
 * Security Features:
 * - Environment variable validation
 * - API key encryption/decryption
 * - Rate limiting configuration
 * - Timeout and retry settings
 * - Sandbox/production environment switching
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const crypto = require('crypto');
const logger = require('../utils/logger');

/**
 * VTpass API Configuration Class
 * Manages all VTpass-related configuration with security best practices
 */
class VTpassConfig {
  constructor() {
    this.validateEnvironment();
    this.initializeConfig();
  }

  /**
   * Validate required environment variables
   * Ensures all necessary VTpass credentials are present
   */
  validateEnvironment() {
    const requiredVars = [
      'VTPASS_API_KEY',
      'VTPASS_SECRET_KEY', 
      'VTPASS_PUBLIC_KEY'
    ];

    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      const error = `Missing required VTpass environment variables: ${missingVars.join(', ')}`;
      logger.error('❌ [VTPASS_CONFIG] Environment validation failed:', error);
      throw new Error(error);
    }

    logger.info('✅ [VTPASS_CONFIG] Environment variables validated successfully');
  }

  /**
   * Initialize VTpass configuration
   * Sets up all configuration parameters with defaults
   */
  initializeConfig() {
    // Environment detection
    this.isProduction = process.env.NODE_ENV === 'production';
    this.isSandbox = process.env.MOCK_VTPASS === 'true' || !this.isProduction;
    
    // API Configuration
    this.config = {
      // Authentication
      apiKey: process.env.VTPASS_API_KEY,
      secretKey: process.env.VTPASS_SECRET_KEY,
      publicKey: process.env.VTPASS_PUBLIC_KEY,
      
      // Endpoints
      baseUrl: this.isProduction 
        ? (process.env.VTPASS_BASE_URL || 'https://vtpass.com/api')
        : (process.env.VTPASS_SANDBOX_URL || 'https://sandbox.vtpass.com/api'),
      
      // Service Configuration - Support for all Nigerian networks
      services: {
        mtn: 'mtn',
        glo: 'glo',
        airtel: 'airtel',
        etisalat: 'etisalat' // 9mobile (formerly Etisalat)
      },
      
      // Security Settings
      timeout: parseInt(process.env.VTPASS_TIMEOUT) || 30000, // 30 seconds
      maxRetries: parseInt(process.env.VTPASS_MAX_RETRIES) || 3,
      
      // Rate Limiting
      rateLimitPerMinute: parseInt(process.env.VTPASS_RATE_LIMIT_PER_MINUTE) || 60,
      
      // Transaction Limits
      minAmount: parseInt(process.env.VTPASS_MIN_AMOUNT) || 50, // ₦50
      maxAmount: parseInt(process.env.VTPASS_MAX_AMOUNT) || 50000, // ₦50,000
      
      // Request Headers
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'PayVendy-VTU/1.0.0',
        'X-API-Version': 'v1'
      }
    };

    // Log configuration (without sensitive data)
    logger.info('✅ [VTPASS_CONFIG] Configuration initialized:', {
      environment: this.isProduction ? 'production' : 'sandbox',
      baseUrl: this.config.baseUrl,
      supportedNetworks: Object.keys(this.config.services),
      timeout: this.config.timeout,
      maxRetries: this.config.maxRetries,
      rateLimitPerMinute: this.config.rateLimitPerMinute,
      minAmount: this.config.minAmount,
      maxAmount: this.config.maxAmount
    });
  }

  /**
   * Get API endpoints
   * Returns all VTpass API endpoints
   */
  getEndpoints() {
    return {
      purchase: `${this.config.baseUrl}/pay`,
      query: `${this.config.baseUrl}/requery`,
      balance: `${this.config.baseUrl}/balance`,
      services: `${this.config.baseUrl}/services`
    };
  }

  /**
   * Get authentication headers
   * Generates secure authentication headers for API requests
   */
  getAuthHeaders() {
    return {
      ...this.config.headers,
      'api-key': this.config.apiKey,
      'secret-key': this.config.secretKey,
      'public-key': this.config.publicKey
    };
  }

  /**
   * Generate request signature
   * Creates HMAC signature for request authentication
   * 
   * @param {Object} payload - Request payload
   * @param {string} timestamp - Request timestamp
   * @returns {string} HMAC signature
   */
  generateSignature(payload, timestamp) {
    try {
      const dataToSign = JSON.stringify(payload) + timestamp;
      const signature = crypto
        .createHmac('sha256', this.config.secretKey)
        .update(dataToSign)
        .digest('hex');
      
      logger.debug('🔐 [VTPASS_CONFIG] Request signature generated');
      return signature;
    } catch (error) {
      logger.error('❌ [VTPASS_CONFIG] Signature generation failed:', error);
      throw new Error('Failed to generate request signature');
    }
  }

  /**
   * Validate transaction amount
   * Ensures amount is within allowed limits
   * 
   * @param {number} amount - Transaction amount
   * @returns {boolean} Validation result
   */
  validateAmount(amount) {
    const numAmount = parseFloat(amount);
    
    if (isNaN(numAmount) || numAmount <= 0) {
      return { valid: false, error: 'Amount must be a positive number' };
    }
    
    if (numAmount < this.config.minAmount) {
      return { 
        valid: false, 
        error: `Minimum amount is ₦${this.config.minAmount}` 
      };
    }
    
    if (numAmount > this.config.maxAmount) {
      return { 
        valid: false, 
        error: `Maximum amount is ₦${this.config.maxAmount}` 
      };
    }
    
    return { valid: true };
  }

  /**
   * Network prefixes for all Nigerian networks
   */
  getNetworkPrefixes() {
    return {
      mtn: ['0803', '0806', '0813', '0816', '0903', '0906', '0913', '0916'],
      glo: ['0805', '0807', '0811', '0815', '0905', '0915'],
      airtel: ['0802', '0808', '0812', '0901', '0902', '0904', '0907', '0912'],
      etisalat: ['0809', '0817', '0818', '0908', '0909'] // 9mobile (formerly Etisalat)
    };
  }

  /**
   * Detect network from phone number
   *
   * @param {string} phone - Phone number
   * @returns {Object} Network detection result
   */
  detectNetwork(phone) {
    const cleanPhone = this.normalizePhoneNumber(phone);

    if (!cleanPhone.valid) {
      return cleanPhone;
    }

    const prefix = cleanPhone.normalized.substring(0, 4);
    const networkPrefixes = this.getNetworkPrefixes();

    for (const [network, prefixes] of Object.entries(networkPrefixes)) {
      if (prefixes.includes(prefix)) {
        return {
          valid: true,
          network,
          networkName: this.getNetworkName(network),
          serviceId: this.config.services[network],
          normalized: cleanPhone.normalized,
          international: cleanPhone.international,
          prefix
        };
      }
    }

    return {
      valid: false,
      error: 'Phone number does not belong to any supported network',
      normalized: null,
      network: null
    };
  }

  /**
   * Normalize phone number to standard format
   *
   * @param {string} phone - Phone number
   * @returns {Object} Normalization result
   */
  normalizePhoneNumber(phone) {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');

    // Check if phone starts with country code
    let normalizedPhone = cleanPhone;
    if (cleanPhone.startsWith('234')) {
      normalizedPhone = '0' + cleanPhone.substring(3);
    } else if (cleanPhone.startsWith('+234')) {
      normalizedPhone = '0' + cleanPhone.substring(4);
    }

    // Validate length (11 digits for Nigerian numbers)
    if (normalizedPhone.length !== 11) {
      return {
        valid: false,
        error: 'Phone number must be 11 digits',
        normalized: null
      };
    }

    // Validate that it starts with 0
    if (!normalizedPhone.startsWith('0')) {
      return {
        valid: false,
        error: 'Invalid Nigerian phone number format',
        normalized: null
      };
    }

    return {
      valid: true,
      normalized: normalizedPhone,
      international: '234' + normalizedPhone.substring(1)
    };
  }

  /**
   * Get network display name
   *
   * @param {string} network - Network code
   * @returns {string} Network display name
   */
  getNetworkName(network) {
    const networkNames = {
      mtn: 'MTN Nigeria',
      glo: 'Globacom (GLO)',
      airtel: 'Airtel Nigeria',
      etisalat: '9mobile (formerly Etisalat)'
    };
    return networkNames[network] || network.toUpperCase();
  }

  /**
   * Validate phone number for specific network (backward compatibility)
   *
   * @param {string} phone - Phone number
   * @param {string} network - Network to validate against (optional)
   * @returns {Object} Validation result
   */
  validatePhone(phone, network = null) {
    const detection = this.detectNetwork(phone);

    if (!detection.valid) {
      return detection;
    }

    // If specific network is requested, validate against it
    if (network && detection.network !== network) {
      return {
        valid: false,
        error: `Phone number is not a ${this.getNetworkName(network)} number`,
        normalized: null,
        detectedNetwork: detection.network,
        detectedNetworkName: detection.networkName
      };
    }

    return detection;
  }

  /**
   * Validate phone number for MTN (backward compatibility)
   *
   * @param {string} phone - Phone number
   * @returns {Object} Validation result
   */
  validateMTNPhone(phone) {
    return this.validatePhone(phone, 'mtn');
  }

  /**
   * Get configuration object
   * Returns the complete configuration (without sensitive data for logging)
   */
  getConfig() {
    return {
      ...this.config,
      // Remove sensitive data
      apiKey: '***HIDDEN***',
      secretKey: '***HIDDEN***',
      publicKey: '***HIDDEN***'
    };
  }

  /**
   * Get raw configuration (with sensitive data)
   * Use only when actually making API calls
   */
  getRawConfig() {
    return this.config;
  }
}

// Create singleton instance
const vtpassConfig = new VTpassConfig();

module.exports = vtpassConfig;
