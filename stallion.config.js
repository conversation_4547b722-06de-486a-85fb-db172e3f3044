/**
 * React Native Stallion Configuration
 *
 * Configuration for OTA (Over-The-Air) updates using React Native Stallion
 * Uses Stallion's hosted infrastructure - no custom backend required
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

module.exports = {
  // ============================================================================
  // APP IDENTIFICATION
  // ============================================================================

  /**
   * App name - Used to identify your app on Stallion's servers
   * Should match your app's name in package.json
   */
  appName: 'payvendy',

  /**
   * App version - Should match your app's version in package.json
   * Used for version compatibility checks
   */
  appVersion: '1.0.0',

  /**
   * Build number - Incremented with each build
   * Used for determining update availability
   */
  buildNumber: 1,

  // ============================================================================
  // UPDATE BEHAVIOR
  // ============================================================================

  /**
   * Automatically check for updates on app start
   */
  checkOnStart: true,

  /**
   * Check for updates when app comes to foreground
   */
  checkOnResume: true,

  /**
   * Automatically download and install updates
   * Set to false for manual update control
   */
  autoUpdate: true,

  /**
   * Install updates immediately for mandatory updates
   */
  installMandatoryImmediately: true,

  // ============================================================================
  // ROLLBACK AND RECOVERY
  // ============================================================================

  /**
   * Enable automatic rollback on crash
   */
  enableRollback: true,

  /**
   * Number of crashes before automatic rollback
   */
  rollbackThreshold: 3,

  // ============================================================================
  // DEVELOPMENT
  // ============================================================================

  /**
   * Enable debug logging
   */
  debug: __DEV__,
};
