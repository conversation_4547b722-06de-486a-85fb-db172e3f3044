/**
 * React Native Stallion Configuration
 * 
 * Configuration for OTA (Over-The-Air) updates using React Native Stallion
 * This replaces the previous custom OTA solution with Stallion's modern approach
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const { Platform } = require('react-native');

module.exports = {
  // ============================================================================
  // SERVER CONFIGURATION
  // ============================================================================
  
  /**
   * Update server URL - Replace with your actual Stallion update server
   * This should be your backend server that hosts the Stallion update bundles
   */
  updateServer: process.env.STALLION_UPDATE_SERVER || 'https://api.payvendy.name.ng/stallion',
  
  /**
   * App version - Should match your app's version in package.json
   * Used for version compatibility checks
   */
  appVersion: '1.0.0',
  
  /**
   * Build number - Incremented with each build
   * Used for determining update availability
   */
  buildNumber: 1,

  // ============================================================================
  // UPDATE BEHAVIOR
  // ============================================================================
  
  /**
   * Automatically check for updates on app start and resume
   */
  autoCheck: true,
  
  /**
   * Check for updates when app comes to foreground
   */
  checkOnResume: true,
  
  /**
   * Interval for periodic update checks (in milliseconds)
   * Set to 0 to disable periodic checks
   */
  checkInterval: 30 * 60 * 1000, // 30 minutes
  
  /**
   * Automatically download available updates
   */
  autoDownload: true,
  
  /**
   * Automatically install downloaded updates
   * If false, updates will be installed on next app restart
   */
  autoInstall: false,
  
  /**
   * Install updates immediately for mandatory updates
   */
  installMandatoryImmediately: true,

  // ============================================================================
  // FALLBACK AND RECOVERY
  // ============================================================================
  
  /**
   * Fallback to cached bundles if update fails
   */
  fallbackToCached: true,
  
  /**
   * Enable automatic rollback on crash
   */
  enableRollback: true,
  
  /**
   * Number of crashes before automatic rollback
   */
  rollbackThreshold: 3,
  
  /**
   * Time window for crash detection (in milliseconds)
   */
  crashDetectionWindow: 5 * 60 * 1000, // 5 minutes

  // ============================================================================
  // PLATFORM SPECIFIC
  // ============================================================================
  
  /**
   * Platform-specific configurations
   */
  platforms: {
    ios: {
      enabled: true,
      bundleIdentifier: 'com.payvendy.app',
    },
    android: {
      enabled: true,
      packageName: 'com.payvendy.app',
    },
  },

  // ============================================================================
  // SECURITY
  // ============================================================================
  
  /**
   * Enable bundle signature verification
   */
  verifySignature: true,
  
  /**
   * Public key for signature verification
   * Replace with your actual public key
   */
  publicKey: process.env.STALLION_PUBLIC_KEY || '',
  
  /**
   * Enable SSL certificate pinning for update downloads
   */
  enableSSLPinning: true,

  // ============================================================================
  // DEVELOPMENT
  // ============================================================================
  
  /**
   * Enable debug logging
   */
  debug: __DEV__,
  
  /**
   * Development server URL for local testing
   */
  devServer: 'http://localhost:3000/stallion',
  
  /**
   * Use development server in debug mode
   */
  useDevServer: __DEV__,

  // ============================================================================
  // ANALYTICS AND MONITORING
  // ============================================================================
  
  /**
   * Enable update analytics
   */
  enableAnalytics: true,
  
  /**
   * Analytics endpoint
   */
  analyticsEndpoint: process.env.STALLION_ANALYTICS_ENDPOINT || 'https://api.payvendy.name.ng/stallion/analytics',
  
  /**
   * Enable crash reporting for update-related crashes
   */
  enableCrashReporting: true,

  // ============================================================================
  // UI CONFIGURATION
  // ============================================================================
  
  /**
   * Show update progress to user
   */
  showProgress: true,
  
  /**
   * Show update notifications
   */
  showNotifications: true,
  
  /**
   * Custom update messages
   */
  messages: {
    checking: 'Checking for updates...',
    downloading: 'Downloading update...',
    installing: 'Installing update...',
    mandatory: 'A mandatory update is available. The app will restart after installation.',
    optional: 'An update is available. Would you like to install it?',
    error: 'Update failed. Please try again later.',
    rollback: 'Update caused issues and has been rolled back.',
  },

  // ============================================================================
  // ADVANCED CONFIGURATION
  // ============================================================================
  
  /**
   * Maximum download size (in bytes)
   */
  maxDownloadSize: 50 * 1024 * 1024, // 50MB
  
  /**
   * Download timeout (in milliseconds)
   */
  downloadTimeout: 5 * 60 * 1000, // 5 minutes
  
  /**
   * Maximum number of download retries
   */
  maxRetries: 3,
  
  /**
   * Retry delay (in milliseconds)
   */
  retryDelay: 5000, // 5 seconds
  
  /**
   * Enable differential updates (smaller update sizes)
   */
  enableDifferentialUpdates: true,
  
  /**
   * Minimum background time before installing update (in milliseconds)
   */
  minimumBackgroundDuration: 10000, // 10 seconds
};
