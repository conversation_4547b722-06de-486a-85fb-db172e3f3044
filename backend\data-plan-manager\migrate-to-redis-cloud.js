/**
 * Redis Cloud Migration Script
 * 
 * Helps migrate from local Redis to Redis Cloud
 * Updates configuration and tests connection
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');

class RedisCloudMigrator {
  constructor() {
    this.envPath = path.join(__dirname, '.env');
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  /**
   * Start migration process
   */
  async migrate() {
    console.log('☁️ [REDIS_CLOUD_MIGRATOR] Starting Redis Cloud migration...\n');

    try {
      // Step 1: Collect Redis Cloud credentials
      const credentials = await this.collectCredentials();
      
      // Step 2: Update .env file
      await this.updateEnvironmentFile(credentials);
      
      // Step 3: Test connection
      await this.testConnection();
      
      // Step 4: Show next steps
      this.showNextSteps();
      
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
    } finally {
      this.rl.close();
    }
  }

  /**
   * Collect Redis Cloud credentials from user
   */
  async collectCredentials() {
    console.log('🔑 Please provide your Redis Cloud credentials:\n');
    
    const host = await this.question('Redis Host (e.g., redis-12345.c123.us-east-1-1.ec2.cloud.redislabs.com): ');
    const port = await this.question('Redis Port (e.g., 12345): ');
    const password = await this.question('Redis Password: ');
    const db = await this.question('Redis DB (default: 0): ') || '0';
    const tls = await this.question('Use TLS? (y/n, default: y): ') || 'y';

    // Validate inputs
    if (!host || !port || !password) {
      throw new Error('Host, port, and password are required');
    }

    if (isNaN(parseInt(port))) {
      throw new Error('Port must be a number');
    }

    return {
      host: host.trim(),
      port: parseInt(port),
      password: password.trim(),
      db: parseInt(db),
      tls: tls.toLowerCase().startsWith('y')
    };
  }

  /**
   * Update .env file with new credentials
   */
  async updateEnvironmentFile(credentials) {
    console.log('\n📝 Updating .env file...');

    try {
      // Read current .env file
      let envContent = await fs.readFile(this.envPath, 'utf8');
      
      // Update Redis configuration
      envContent = this.updateEnvVariable(envContent, 'REDIS_HOST', credentials.host);
      envContent = this.updateEnvVariable(envContent, 'REDIS_PORT', credentials.port.toString());
      envContent = this.updateEnvVariable(envContent, 'REDIS_PASSWORD', credentials.password);
      envContent = this.updateEnvVariable(envContent, 'REDIS_DB', credentials.db.toString());
      envContent = this.updateEnvVariable(envContent, 'REDIS_TLS', credentials.tls.toString());

      // Write updated content
      await fs.writeFile(this.envPath, envContent);
      
      console.log('✅ Environment file updated successfully');
      
    } catch (error) {
      throw new Error(`Failed to update .env file: ${error.message}`);
    }
  }

  /**
   * Update environment variable in content
   */
  updateEnvVariable(content, key, value) {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    const newLine = `${key}=${value}`;
    
    if (regex.test(content)) {
      return content.replace(regex, newLine);
    } else {
      return content + `\n${newLine}`;
    }
  }

  /**
   * Test Redis Cloud connection
   */
  async testConnection() {
    console.log('\n🧪 Testing Redis Cloud connection...');

    try {
      // Reload environment variables
      delete require.cache[require.resolve('dotenv')];
      require('dotenv').config({ path: this.envPath });

      // Test with our Redis Cloud tester
      const RedisCloudTester = require('./test-redis-cloud');
      const tester = new RedisCloudTester();
      
      // Run basic connection test
      await tester.testConnection();
      
      console.log('✅ Redis Cloud connection test completed');
      
    } catch (error) {
      console.error('❌ Connection test failed:', error.message);
      console.log('\n💡 Troubleshooting tips:');
      console.log('   1. Verify your Redis Cloud credentials');
      console.log('   2. Check if your IP is whitelisted in Redis Cloud');
      console.log('   3. Ensure TLS is enabled if required');
      console.log('   4. Try running: node data-plan-manager/test-redis-cloud.js');
    }
  }

  /**
   * Show next steps
   */
  showNextSteps() {
    console.log('\n🎉 Redis Cloud migration completed!\n');
    console.log('📋 Next Steps:');
    console.log('   1. Run full test: node data-plan-manager/test-redis-cloud.js');
    console.log('   2. Test data plan manager: node data-plan-manager/test-data-plan-manager.js');
    console.log('   3. Start your backend server: npm start');
    console.log('   4. Monitor Redis Cloud dashboard for usage');
    console.log('');
    console.log('🔧 Configuration Summary:');
    console.log(`   Host: ${process.env.REDIS_HOST}`);
    console.log(`   Port: ${process.env.REDIS_PORT}`);
    console.log(`   TLS: ${process.env.REDIS_TLS}`);
    console.log(`   DB: ${process.env.REDIS_DB}`);
    console.log('');
    console.log('💰 Monitor your Redis Cloud usage at: https://app.redislabs.com/');
    console.log('📊 Your cache is now ready for 1M+ users! 🚀');
  }

  /**
   * Prompt user for input
   */
  question(prompt) {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  const migrator = new RedisCloudMigrator();
  migrator.migrate().catch(console.error);
}

module.exports = RedisCloudMigrator;
