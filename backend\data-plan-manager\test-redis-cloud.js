/**
 * Redis Cloud Connection Test
 * 
 * Test script to verify Redis Cloud connection and performance
 * Run this after setting up Redis Cloud credentials
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

require('dotenv').config({ path: require('path').join(__dirname, '.env') });
const cacheManager = require('./services/cacheManager');

class RedisCloudTester {
  constructor() {
    this.testResults = [];
  }

  /**
   * Run all Redis Cloud tests
   */
  async runAllTests() {
    console.log('☁️ [REDIS_CLOUD_TEST] Starting Redis Cloud connection tests...\n');

    // Show configuration
    this.showConfiguration();

    try {
      await this.testConnection();
      await this.testBasicOperations();
      await this.testPerformance();
      await this.testConcurrency();
      await this.testFailover();

      this.printTestResults();
    } catch (error) {
      console.error('❌ [REDIS_CLOUD_TEST] Test suite failed:', error.message);
    }
  }

  /**
   * Show current configuration
   */
  showConfiguration() {
    console.log('🔧 [REDIS_CLOUD_TEST] Current Configuration:');
    console.log('   REDIS_HOST:', process.env.REDIS_HOST || 'NOT SET');
    console.log('   REDIS_PORT:', process.env.REDIS_PORT || 'NOT SET');
    console.log('   REDIS_PASSWORD:', process.env.REDIS_PASSWORD ? '***' + process.env.REDIS_PASSWORD.slice(-3) : 'NOT SET');
    console.log('   REDIS_TLS:', process.env.REDIS_TLS || 'false');
    console.log('   REDIS_DB:', process.env.REDIS_DB || '0');
    
    const isCloud = process.env.REDIS_HOST && 
                   !process.env.REDIS_HOST.includes('localhost') && 
                   !process.env.REDIS_HOST.includes('127.0.0.1');
    console.log('   Detected as:', isCloud ? 'Redis Cloud ☁️' : 'Local Redis 🏠');
    console.log('');
  }

  /**
   * Test basic connection
   */
  async testConnection() {
    console.log('🔌 Testing Redis Connection...');
    
    try {
      const health = await cacheManager.healthCheck();
      
      if (health.status === 'healthy') {
        this.addTestResult('Redis Connection', true, `Connected successfully (${health.latency}ms latency)`);
        console.log(`✅ Connection test passed - ${health.latency}ms latency`);
      } else {
        this.addTestResult('Redis Connection', false, `Connection failed: ${health.error || 'Unknown error'}`);
        console.log('❌ Connection test failed');
      }
      
      console.log('');
    } catch (error) {
      this.addTestResult('Redis Connection', false, error.message);
      console.log('❌ Connection test failed:', error.message, '\n');
    }
  }

  /**
   * Test basic Redis operations
   */
  async testBasicOperations() {
    console.log('⚙️ Testing Basic Operations...');
    
    try {
      // Test SET operation
      const testKey = 'test:redis:cloud:' + Date.now();
      const testValue = { message: 'Hello Redis Cloud!', timestamp: Date.now() };
      
      const setResult = await cacheManager.set(testKey, testValue, 60);
      if (setResult) {
        this.addTestResult('Redis SET', true, 'Data stored successfully');
        console.log('✅ SET operation passed');
      } else {
        this.addTestResult('Redis SET', false, 'Failed to store data');
        console.log('❌ SET operation failed');
      }

      // Test GET operation
      const getValue = await cacheManager.get(testKey);
      if (getValue && getValue.message === testValue.message) {
        this.addTestResult('Redis GET', true, 'Data retrieved successfully');
        console.log('✅ GET operation passed');
      } else {
        this.addTestResult('Redis GET', false, 'Failed to retrieve data');
        console.log('❌ GET operation failed');
      }

      // Test DELETE operation
      const deleteResult = await cacheManager.delete(testKey);
      if (deleteResult) {
        this.addTestResult('Redis DELETE', true, 'Data deleted successfully');
        console.log('✅ DELETE operation passed');
      } else {
        this.addTestResult('Redis DELETE', false, 'Failed to delete data');
        console.log('❌ DELETE operation failed');
      }

      console.log('');
    } catch (error) {
      this.addTestResult('Basic Operations', false, error.message);
      console.log('❌ Basic operations test failed:', error.message, '\n');
    }
  }

  /**
   * Test performance
   */
  async testPerformance() {
    console.log('🚀 Testing Performance...');
    
    try {
      const iterations = 100;
      const testData = { 
        plans: Array.from({length: 50}, (_, i) => ({
          id: i,
          name: `Plan ${i}`,
          price: Math.random() * 1000
        }))
      };

      // Test write performance
      const writeStart = Date.now();
      const writePromises = [];
      
      for (let i = 0; i < iterations; i++) {
        const key = `perf:write:${i}`;
        writePromises.push(cacheManager.set(key, testData, 300));
      }
      
      await Promise.all(writePromises);
      const writeTime = Date.now() - writeStart;
      const writeOpsPerSec = Math.round((iterations / writeTime) * 1000);

      this.addTestResult('Write Performance', true, `${writeOpsPerSec} ops/sec (${writeTime}ms for ${iterations} writes)`);
      console.log(`✅ Write performance: ${writeOpsPerSec} ops/sec`);

      // Test read performance
      const readStart = Date.now();
      const readPromises = [];
      
      for (let i = 0; i < iterations; i++) {
        const key = `perf:write:${i}`;
        readPromises.push(cacheManager.get(key));
      }
      
      await Promise.all(readPromises);
      const readTime = Date.now() - readStart;
      const readOpsPerSec = Math.round((iterations / readTime) * 1000);

      this.addTestResult('Read Performance', true, `${readOpsPerSec} ops/sec (${readTime}ms for ${iterations} reads)`);
      console.log(`✅ Read performance: ${readOpsPerSec} ops/sec`);

      // Cleanup
      await cacheManager.clearPattern('perf:*');

      console.log('');
    } catch (error) {
      this.addTestResult('Performance Test', false, error.message);
      console.log('❌ Performance test failed:', error.message, '\n');
    }
  }

  /**
   * Test concurrent operations
   */
  async testConcurrency() {
    console.log('🔄 Testing Concurrency...');
    
    try {
      const concurrentOps = 50;
      const promises = [];

      // Create concurrent read/write operations
      for (let i = 0; i < concurrentOps; i++) {
        const key = `concurrent:${i}`;
        const value = { id: i, data: `concurrent-test-${i}` };
        
        promises.push(
          cacheManager.set(key, value, 60).then(() => 
            cacheManager.get(key)
          )
        );
      }

      const start = Date.now();
      const results = await Promise.all(promises);
      const duration = Date.now() - start;

      const successCount = results.filter(r => r && r.data).length;
      
      if (successCount === concurrentOps) {
        this.addTestResult('Concurrency Test', true, `${concurrentOps} concurrent ops completed in ${duration}ms`);
        console.log(`✅ Concurrency test passed: ${concurrentOps} ops in ${duration}ms`);
      } else {
        this.addTestResult('Concurrency Test', false, `Only ${successCount}/${concurrentOps} operations succeeded`);
        console.log(`❌ Concurrency test failed: ${successCount}/${concurrentOps} succeeded`);
      }

      // Cleanup
      await cacheManager.clearPattern('concurrent:*');

      console.log('');
    } catch (error) {
      this.addTestResult('Concurrency Test', false, error.message);
      console.log('❌ Concurrency test failed:', error.message, '\n');
    }
  }

  /**
   * Test failover to fallback cache
   */
  async testFailover() {
    console.log('🛡️ Testing Failover Mechanism...');
    
    try {
      // This test verifies that the system gracefully handles Redis failures
      const stats = await cacheManager.getStats();
      
      if (stats.type === 'redis' && stats.connected) {
        this.addTestResult('Failover Test', true, 'Redis connected, fallback available if needed');
        console.log('✅ Failover mechanism ready (Redis active)');
      } else if (stats.type === 'fallback') {
        this.addTestResult('Failover Test', true, 'Using fallback cache (Redis unavailable)');
        console.log('✅ Failover working (using fallback cache)');
      } else {
        this.addTestResult('Failover Test', false, 'Cache system not responding');
        console.log('❌ Failover test failed');
      }

      console.log('');
    } catch (error) {
      this.addTestResult('Failover Test', false, error.message);
      console.log('❌ Failover test failed:', error.message, '\n');
    }
  }

  /**
   * Add test result
   */
  addTestResult(testName, passed, details) {
    this.testResults.push({
      testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Print test results summary
   */
  async printTestResults() {
    console.log('📊 REDIS CLOUD TEST RESULTS');
    console.log('============================\n');
    
    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.testName}`);
      console.log(`   Details: ${result.details}\n`);
    });
    
    console.log(`Overall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All Redis Cloud tests passed! Ready for production.');
    } else {
      console.log('⚠️ Some tests failed. Check your Redis Cloud configuration.');
    }

    // Show cache statistics
    try {
      const stats = await cacheManager.getStats();
      console.log('\n📈 Cache Statistics:');
      console.log(`   Type: ${stats.type}`);
      console.log(`   Connected: ${stats.connected}`);
      if (stats.memory) {
        console.log(`   Memory Used: ${stats.memory.used_memory_human || 'N/A'}`);
      }
      if (stats.fallbackSize !== undefined) {
        console.log(`   Fallback Cache Size: ${stats.fallbackSize} keys`);
      }
    } catch (error) {
      console.log('\n⚠️ Could not retrieve cache statistics');
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new RedisCloudTester();
  tester.runAllTests().catch(console.error);
}

module.exports = RedisCloudTester;
