#!/bin/bash

# OTA Update Deployment Script
# Automates the process of creating and deploying OTA updates

set -e  # Exit on any error

# Configuration
VERSION=""
BUILD_NUMBER=""
DESCRIPTION=""
IS_MANDATORY=false
PLATFORMS=("android" "ios")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -b|--build)
            BUILD_NUMBER="$2"
            shift 2
            ;;
        -d|--description)
            DESCRIPTION="$2"
            shift 2
            ;;
        -m|--mandatory)
            IS_MANDATORY=true
            shift
            ;;
        -p|--platform)
            IFS=',' read -ra PLATFORMS <<< "$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 -v VERSION -b BUILD_NUMBER -d DESCRIPTION [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -v, --version      Version number (e.g., 1.0.2)"
            echo "  -b, --build        Build number (e.g., 3)"
            echo "  -d, --description  Update description"
            echo "  -m, --mandatory    Mark as mandatory update"
            echo "  -p, --platform     Platforms (android,ios or android or ios)"
            echo "  -h, --help         Show this help"
            echo ""
            echo "Example:"
            echo "  $0 -v 1.0.2 -b 3 -d \"Bug fixes and UI improvements\" -m"
            exit 0
            ;;
        *)
            log_error "Unknown option $1"
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$VERSION" || -z "$BUILD_NUMBER" || -z "$DESCRIPTION" ]]; then
    log_error "Missing required parameters. Use -h for help."
    exit 1
fi

# Create directories
BUNDLES_DIR="./ota-bundles"
BACKEND_BUNDLES_DIR="./backend/storage/app-bundles"

mkdir -p "$BUNDLES_DIR"
mkdir -p "$BACKEND_BUNDLES_DIR"

log_info "Starting OTA update deployment for version $VERSION (build $BUILD_NUMBER)"

# Function to build bundle for a platform
build_platform_bundle() {
    local platform=$1
    log_info "Building $platform bundle..."
    
    # Create platform-specific directory
    local platform_dir="$BUNDLES_DIR/$platform"
    mkdir -p "$platform_dir"
    
    # Build the bundle
    npx react-native bundle \
        --platform "$platform" \
        --dev false \
        --entry-file index.js \
        --bundle-output "$platform_dir/bundle.js" \
        --assets-dest "$platform_dir/assets" \
        --reset-cache
    
    if [[ $? -eq 0 ]]; then
        log_success "$platform bundle created successfully"
    else
        log_error "Failed to create $platform bundle"
        exit 1
    fi
    
    # Create zip file
    local zip_name="vendy-v$VERSION-$platform.zip"
    local zip_path="$BUNDLES_DIR/$zip_name"
    
    cd "$platform_dir"
    zip -r "../$zip_name" bundle.js assets/ > /dev/null 2>&1
    cd - > /dev/null
    
    if [[ -f "$zip_path" ]]; then
        log_success "$platform zip package created: $zip_name"
        
        # Calculate checksum
        local checksum=$(shasum -a 256 "$zip_path" | cut -d' ' -f1)
        local size=$(stat -f%z "$zip_path" 2>/dev/null || stat -c%s "$zip_path" 2>/dev/null)
        
        log_info "$platform checksum: sha256:$checksum"
        log_info "$platform size: $size bytes"
        
        # Copy to backend storage
        cp "$zip_path" "$BACKEND_BUNDLES_DIR/"
        log_success "$platform bundle copied to backend storage"
        
        # Store info for config update
        eval "${platform}_checksum=sha256:$checksum"
        eval "${platform}_size=$size"
        eval "${platform}_filename=$zip_name"
    else
        log_error "Failed to create $platform zip package"
        exit 1
    fi
}

# Build bundles for each platform
for platform in "${PLATFORMS[@]}"; do
    build_platform_bundle "$platform"
done

# Update backend configuration
log_info "Updating backend configuration..."

CONFIG_FILE="./backend/config/app-updates.json"
TEMP_CONFIG="/tmp/app-updates-temp.json"

# Create backup
cp "$CONFIG_FILE" "${CONFIG_FILE}.backup"

# Read existing config or create new one
if [[ -f "$CONFIG_FILE" ]]; then
    EXISTING_CONFIG=$(cat "$CONFIG_FILE")
else
    EXISTING_CONFIG='{"lastUpdated":"","updates":[],"config":{"enableUpdates":true}}'
fi

# Generate new update entries
TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")
NEW_UPDATES=""

for platform in "${PLATFORMS[@]}"; do
    local checksum_var="${platform}_checksum"
    local size_var="${platform}_size"
    local filename_var="${platform}_filename"
    
    local checksum=${!checksum_var}
    local size=${!size_var}
    local filename=${!filename_var}
    
    if [[ -n "$checksum" && -n "$size" && -n "$filename" ]]; then
        local update_entry=$(cat <<EOF
{
  "id": "vendy-v$VERSION-$platform",
  "version": "$VERSION",
  "buildNumber": "$BUILD_NUMBER",
  "platform": "$platform",
  "filename": "$filename",
  "downloadUrl": "/api/v1/app/updates/download/vendy-v$VERSION-$platform",
  "checksum": "$checksum",
  "size": $size,
  "isMandatory": $IS_MANDATORY,
  "description": "$DESCRIPTION",
  "releaseNotes": "$DESCRIPTION",
  "minAppVersion": "1.0.0",
  "maxAppVersion": null,
  "createdAt": "$TIMESTAMP",
  "deployedAt": "$TIMESTAMP",
  "isActive": true
}
EOF
)
        
        if [[ -z "$NEW_UPDATES" ]]; then
            NEW_UPDATES="$update_entry"
        else
            NEW_UPDATES="$NEW_UPDATES,$update_entry"
        fi
    fi
done

# Update the JSON configuration
echo "$EXISTING_CONFIG" | jq --argjson new_updates "[$NEW_UPDATES]" --arg timestamp "$TIMESTAMP" '
  .lastUpdated = $timestamp |
  .updates = ($new_updates + .updates)
' > "$TEMP_CONFIG"

# Validate JSON
if jq empty "$TEMP_CONFIG" 2>/dev/null; then
    mv "$TEMP_CONFIG" "$CONFIG_FILE"
    log_success "Backend configuration updated successfully"
else
    log_error "Failed to update configuration - invalid JSON generated"
    mv "${CONFIG_FILE}.backup" "$CONFIG_FILE"
    exit 1
fi

# Clean up temporary files
rm -rf "$BUNDLES_DIR"

# Summary
log_success "🎉 OTA Update deployment completed!"
echo ""
log_info "📋 Summary:"
echo "   Version: $VERSION"
echo "   Build: $BUILD_NUMBER"
echo "   Platforms: ${PLATFORMS[*]}"
echo "   Mandatory: $IS_MANDATORY"
echo "   Description: $DESCRIPTION"
echo ""
log_info "📱 Next steps:"
echo "   1. Restart your backend server to load the new configuration"
echo "   2. Test the update on a development device"
echo "   3. Monitor update analytics in your backend logs"
echo ""
log_warning "⚠️  Remember to restart your backend server:"
echo "   pm2 restart vendy-backend"
echo "   # or"
echo "   npm run start"
