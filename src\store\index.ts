import { create } from 'zustand'
import { persist, createJSONStorage, subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { MMKV } from 'react-native-mmkv'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { devtools } from 'zustand/middleware'
import { User, UserPreferences, NotificationSettings, PrivacySettings, UserProfileDetails, Transaction, Balance } from '../types/user'
import logger from '../services/productionLogger'

// Initialize MMKV for better performance
const storage = new MMKV({
  id: 'vendy-store',
  encryptionKey: 'vendy-encryption-key-2024', // Use a proper key in production
})

// MMKV storage adapter for Zustand
const mmkvStorage = {
  getItem: (name: string) => {
    const value = storage.getString(name)
    return value ? JSON.parse(value) : null
  },
  setItem: (name: string, value: any) => {
    storage.set(name, JSON.stringify(value))
  },
  removeItem: (name: string) => {
    storage.delete(name)
  },
}

// Enhanced Types (using shared types from ../types/user.ts)

interface AppState {
  // User state
  user: User | null
  isAuthenticated: boolean
  authToken: string | null
  refreshToken: string | null
  
  // Financial state
  balance: Balance | null
  transactions: Record<string, Transaction>
  transactionIds: string[]
  
  // UI state
  theme: 'light' | 'dark' | 'system'
  isLoading: boolean
  loadingStates: Record<string, boolean>
  errors: Record<string, string | null>
  
  // Navigation state
  currentScreen: string
  navigationHistory: string[]
  
  // Performance state
  animationsEnabled: boolean
  cacheEnabled: boolean
  offlineMode: boolean
  
  // Network state
  isOnline: boolean
  lastSyncTime: number
  
  // Feature flags
  featureFlags: Record<string, boolean>
  
  // Cache state
  cache: Record<string, { data: any; timestamp: number; expiresAt: number }>



  // Actions
  setUser: (user: User | null) => void
  updateUser: (updates: Partial<User>) => void
  setAuthenticated: (isAuth: boolean) => void
  setAuthTokens: (accessToken: string | null, refreshToken: string | null) => void
  setBalance: (balance: Balance) => void
  addTransaction: (transaction: Transaction) => void
  updateTransaction: (id: string, updates: Partial<Transaction>) => void
  setTransactions: (transactions: Transaction[]) => void
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  setLoading: (key: string, loading: boolean) => void
  setError: (key: string, error: string | null) => void
  setCurrentScreen: (screen: string) => void
  toggleAnimations: () => void
  setOnlineStatus: (isOnline: boolean) => void
  updateLastSyncTime: () => void
  setFeatureFlag: (key: string, enabled: boolean) => void
  setCacheItem: (key: string, data: any, expiresIn?: number) => void
  getCacheItem: (key: string) => any | null
  clearCache: () => void
  setUpdateStatus: (status: 'checking' | 'downloading' | 'installing' | 'up_to_date' | 'update_available' | 'error' | 'restart_required' | null) => void
  setUpdateProgress: (progress: number) => void
  setUpdateInfo: (info: { isAvailable: boolean; isMandatory: boolean; description?: string; packageSize?: number; version?: string } | null) => void
  reset: () => void
  resetAuth: () => void
}

// Initial state
const initialState = {
  user: null,
  isAuthenticated: false,
  authToken: null,
  refreshToken: null,
  balance: null,
  transactions: {},
  transactionIds: [],
  theme: 'light' as const,
  isLoading: false,
  loadingStates: {},
  errors: {},
  currentScreen: 'Splash',
  navigationHistory: [],
  animationsEnabled: true,
  cacheEnabled: true,
  offlineMode: false,
  isOnline: true,
  lastSyncTime: 0,
  featureFlags: {},
  cache: {},

}

// Create store with multiple middleware
export const useAppStore = create<AppState>()(
  devtools(
    subscribeWithSelector(
      immer(
        persist(
          (set, get) => ({
            ...initialState,
            
            // User Actions
            setUser: (user) => set((state) => {
              state.user = user
            }),
            
            updateUser: (updates) => set((state) => {
              if (state.user) {
                Object.assign(state.user, updates)
              }
            }),
            
            setAuthenticated: (isAuthenticated) => set((state) => {
              state.isAuthenticated = isAuthenticated
            }),
            
            setAuthTokens: (accessToken, refreshToken) => set((state) => {
              state.authToken = accessToken
              state.refreshToken = refreshToken
            }),
            
            // Financial Actions
            setBalance: (balance) => set((state) => {
              state.balance = balance
            }),
            
            addTransaction: (transaction) => set((state) => {
              state.transactions[transaction.id] = transaction
              if (!state.transactionIds.includes(transaction.id)) {
                state.transactionIds.unshift(transaction.id) // Add to beginning for recent first
              }
              
              // Keep only last 1000 transactions for performance
              if (state.transactionIds.length > 1000) {
                const removedIds = state.transactionIds.splice(1000)
                removedIds.forEach(id => delete state.transactions[id])
              }
            }),
            
            updateTransaction: (id, updates) => set((state) => {
              if (state.transactions[id]) {
                Object.assign(state.transactions[id], updates)
              }
            }),
            
            setTransactions: (transactions) => set((state) => {
              state.transactions = {}
              state.transactionIds = []
              
              transactions.forEach(transaction => {
                state.transactions[transaction.id] = transaction
                state.transactionIds.push(transaction.id)
              })
              
              // Sort by creation date (newest first)
              state.transactionIds.sort((a, b) => {
                const dateA = new Date(state.transactions[a].createdAt).getTime()
                const dateB = new Date(state.transactions[b].createdAt).getTime()
                return dateB - dateA
              })
            }),
            
            // UI Actions
            setTheme: (theme) => set((state) => {
              state.theme = theme
            }),
            
            setLoading: (key, loading) => set((state) => {
              if (loading) {
                state.loadingStates[key] = true
              } else {
                delete state.loadingStates[key]
              }
              state.isLoading = Object.keys(state.loadingStates).length > 0
            }),
            
            setError: (key, error) => set((state) => {
              if (error) {
                state.errors[key] = error
              } else {
                delete state.errors[key]
              }
            }),
            
            // Navigation Actions
            setCurrentScreen: (screen) => set((state) => {
              state.currentScreen = screen
              state.navigationHistory.push(screen)
              
              // Keep only last 50 screens
              if (state.navigationHistory.length > 50) {
                state.navigationHistory = state.navigationHistory.slice(-50)
              }
            }),
            
            // Performance Actions
            toggleAnimations: () => set((state) => {
              state.animationsEnabled = !state.animationsEnabled
            }),
            
            // Network Actions
            setOnlineStatus: (isOnline) => set((state) => {
              state.isOnline = isOnline
            }),
            
            updateLastSyncTime: () => set((state) => {
              state.lastSyncTime = Date.now()
            }),
            
            // Feature Flag Actions
            setFeatureFlag: (key, enabled) => set((state) => {
              state.featureFlags[key] = enabled
            }),
            
            // Cache Actions
            setCacheItem: (key, data, expiresIn = 5 * 60 * 1000) => set((state) => {
              const now = Date.now()
              state.cache[key] = {
                data,
                timestamp: now,
                expiresAt: now + expiresIn,
              }
              
              // Clean expired items
              Object.keys(state.cache).forEach(cacheKey => {
                if (state.cache[cacheKey].expiresAt < now) {
                  delete state.cache[cacheKey]
                }
              })
            }),
            
            getCacheItem: (key) => {
              const item = get().cache[key]
              if (!item) return null
              
              if (Date.now() > item.expiresAt) {
                set((state) => {
                  delete state.cache[key]
                })
                return null
              }
              
              return item.data
            },
            
            clearCache: () => set((state) => {
              state.cache = {}
            }),



            // Reset Actions
            reset: () => set(initialState),
            
            resetAuth: () => set((state) => {
              state.user = null
              state.isAuthenticated = false
              state.authToken = null
              state.refreshToken = null
              state.balance = null
              state.transactions = {}
              state.transactionIds = []
            }),
          }),
          {
            name: 'vendy-storage',
            storage: createJSONStorage(() => mmkvStorage),
            // Only persist important data to reduce storage size
            partialize: (state) => ({
              user: state.user,
              isAuthenticated: state.isAuthenticated,
              theme: state.theme,
              animationsEnabled: state.animationsEnabled,
              cacheEnabled: state.cacheEnabled,
              featureFlags: state.featureFlags,
              // Don't persist sensitive tokens or large datasets
              // authToken and refreshToken should be in secure storage
              // transactions should be fetched fresh or cached separately
            }),
            // Migrate old data if needed
            version: 2,
            migrate: (persistedState: any, version: number) => {
              if (version === 0) {
                // Migrate from version 0 to 1
                return {
                  ...persistedState,
                  loadingStates: {},
                  errors: {},
                  navigationHistory: [],
                  cacheEnabled: true,
                  offlineMode: false,
                  isOnline: true,
                  lastSyncTime: 0,
                  featureFlags: {},
                  cache: {},
                }
              }
              if (version === 1) {
                // Migrate from version 1 to 2
                return {
                  ...persistedState,
                  cache: {},
                }
              }
              return persistedState
            },
          }
        )
      )
    ),
    {
      name: 'vendy-store',
    }
  )
)

// Selector hooks for better performance
export const useUser = () => useAppStore((state) => state.user)
export const useAuth = () => useAppStore((state) => ({
  isAuthenticated: state.isAuthenticated,
  authToken: state.authToken,
  refreshToken: state.refreshToken,
}))

export const useBalance = () => useAppStore((state) => state.balance)
export const useTransactions = () => useAppStore((state) => ({
  transactions: state.transactions,
  transactionIds: state.transactionIds,
}))

export const useTheme = () => useAppStore((state) => state.theme)
export const useLoading = () => useAppStore((state) => ({
  isLoading: state.isLoading,
  loadingStates: state.loadingStates,
}))

export const useErrors = () => useAppStore((state) => state.errors)
export const useNavigation = () => useAppStore((state) => ({
  currentScreen: state.currentScreen,
  navigationHistory: state.navigationHistory,
}))

export const useNetwork = () => useAppStore((state) => ({
  isOnline: state.isOnline,
  lastSyncTime: state.lastSyncTime,
}))

export const useFeatureFlags = () => useAppStore((state) => state.featureFlags)
export const useCache = () => useAppStore((state) => state.cache)

// Action hooks
export const useUserActions = () => useAppStore((state) => ({
  setUser: state.setUser,
  updateUser: state.updateUser,
  setAuthenticated: state.setAuthenticated,
  setAuthTokens: state.setAuthTokens,
  resetAuth: state.resetAuth,
}))

export const useFinancialActions = () => useAppStore((state) => ({
  setBalance: state.setBalance,
  addTransaction: state.addTransaction,
  updateTransaction: state.updateTransaction,
  setTransactions: state.setTransactions,
}))

export const useUIActions = () => useAppStore((state) => ({
  setTheme: state.setTheme,
  setLoading: state.setLoading,
  setError: state.setError,
  setCurrentScreen: state.setCurrentScreen,
  toggleAnimations: state.toggleAnimations,
}))

export const useNetworkActions = () => useAppStore((state) => ({
  setOnlineStatus: state.setOnlineStatus,
  updateLastSyncTime: state.updateLastSyncTime,
}))

export const useCacheActions = () => useAppStore((state) => ({
  setCacheItem: state.setCacheItem,
  getCacheItem: state.getCacheItem,
  clearCache: state.clearCache,
}))

export const useFeatureFlagActions = () => useAppStore((state) => ({
  setFeatureFlag: state.setFeatureFlag,
}))

// Combined actions for convenience
export const useAppActions = () => useAppStore((state) => ({
  // User
  setUser: state.setUser,
  updateUser: state.updateUser,
  setAuthenticated: state.setAuthenticated,
  setAuthTokens: state.setAuthTokens,
  resetAuth: state.resetAuth,
  
  // Financial
  setBalance: state.setBalance,
  addTransaction: state.addTransaction,
  updateTransaction: state.updateTransaction,
  setTransactions: state.setTransactions,
  
  // UI
  setTheme: state.setTheme,
  setLoading: state.setLoading,
  setError: state.setError,
  setCurrentScreen: state.setCurrentScreen,
  toggleAnimations: state.toggleAnimations,
  
  // Network
  setOnlineStatus: state.setOnlineStatus,
  updateLastSyncTime: state.updateLastSyncTime,
  
  // Cache
  setCacheItem: state.setCacheItem,
  getCacheItem: state.getCacheItem,
  clearCache: state.clearCache,
  
  // Feature Flags
  setFeatureFlag: state.setFeatureFlag,
  
  // General
  reset: state.reset,
}))

// Store subscriptions for external services
export const subscribeToAuthChanges = (callback: (isAuthenticated: boolean) => void) => {
  return useAppStore.subscribe(
    (state) => state.isAuthenticated,
    callback
  )
}

export const subscribeToNetworkChanges = (callback: (isOnline: boolean) => void) => {
  return useAppStore.subscribe(
    (state) => state.isOnline,
    callback
  )
}

export const subscribeToThemeChanges = (callback: (theme: string) => void) => {
  return useAppStore.subscribe(
    (state) => state.theme,
    callback
  )
}



// Store utilities
export const getStoreSnapshot = () => useAppStore.getState()

export const clearAllData = () => {
  useAppStore.getState().reset()
  // Also clear MMKV storage
  storage.clearAll()
}

// Performance monitoring for store
if (__DEV__) {
  useAppStore.subscribe(
    (state) => state,
    (state, prevState) => {
      // Log state changes in development
      const changes: string[] = []
      
      if (state.user !== prevState.user) changes.push('user')
      if (state.isAuthenticated !== prevState.isAuthenticated) changes.push('auth')
      if (state.balance !== prevState.balance) changes.push('balance')
      if (state.transactionIds.length !== prevState.transactionIds.length) changes.push('transactions')
      if (state.theme !== prevState.theme) changes.push('theme')
      if (state.isLoading !== prevState.isLoading) changes.push('loading')
      if (state.isOnline !== prevState.isOnline) changes.push('network')
      
      if (changes.length > 0) {
logger.info('🏪 [Store] State changed:', changes.join(', '))
      }
    }
  )
}