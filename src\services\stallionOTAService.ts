/**
 * Stallion OTA Service
 * 
 * Service for handling Over-The-Air updates using React Native Stallion
 * Provides a clean interface for update management with proper error handling
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { AppState, AppStateStatus } from 'react-native';
import Stallion from 'react-native-stallion';
import logger from './productionLogger';

// Update status types
export enum UpdateStatus {
  CHECKING = 'checking',
  DOWNLOADING = 'downloading',
  INSTALLING = 'installing',
  UP_TO_DATE = 'up_to_date',
  UPDATE_AVAILABLE = 'update_available',
  ERROR = 'error',
  RESTART_REQUIRED = 'restart_required',
}

// Update info interface
export interface UpdateInfo {
  isAvailable: boolean;
  isMandatory: boolean;
  version: string;
  buildNumber: number;
  description?: string;
  packageSize?: number;
  downloadUrl?: string;
}

// Update progress interface
export interface UpdateProgress {
  bytesReceived: number;
  totalBytes: number;
  percentage: number;
}

// Event listeners type
type UpdateEventListener = (status: UpdateStatus, info?: UpdateInfo, progress?: UpdateProgress) => void;

class StallionOTAService {
  private isInitialized: boolean = false;
  private currentStatus: UpdateStatus = UpdateStatus.UP_TO_DATE;
  private currentUpdateInfo: UpdateInfo | null = null;
  private eventListeners: UpdateEventListener[] = [];
  private appStateSubscription: any = null;
  private checkInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize the Stallion OTA service
   */
  async initialize(): Promise<boolean> {
    try {
      logger.info('[STALLION_OTA] Initializing Stallion OTA service...', { module: 'stallionOTA', action: 'initialize' });

      // Initialize Stallion
      await Stallion.init();

      // Set up event listeners
      this.setupEventListeners();

      // Set up app state listener for background/foreground checks
      this.setupAppStateListener();

      // Start periodic checks if enabled
      this.startPeriodicChecks();

      // Perform initial update check
      await this.checkForUpdates();

      this.isInitialized = true;
      logger.info('[STALLION_OTA] Stallion OTA service initialized successfully', { module: 'stallionOTA', action: 'initialize' });

      return true;
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to initialize Stallion OTA service', error, 'stallionOTA');
      return false;
    }
  }

  /**
   * Set up Stallion event listeners
   */
  private setupEventListeners(): void {
    // Update available
    Stallion.on('updateAvailable', (updateInfo: any) => {
      logger.info('[STALLION_OTA] Update available', { updateInfo }, 'stallionOTA');
      
      const info: UpdateInfo = {
        isAvailable: true,
        isMandatory: updateInfo.isMandatory || false,
        version: updateInfo.version,
        buildNumber: updateInfo.buildNumber,
        description: updateInfo.description,
        packageSize: updateInfo.packageSize,
        downloadUrl: updateInfo.downloadUrl,
      };

      this.currentUpdateInfo = info;
      this.updateStatus(UpdateStatus.UPDATE_AVAILABLE, info);
    });

    // Download progress
    Stallion.on('downloadProgress', (progress: any) => {
      const progressInfo: UpdateProgress = {
        bytesReceived: progress.bytesReceived,
        totalBytes: progress.totalBytes,
        percentage: Math.round((progress.bytesReceived / progress.totalBytes) * 100),
      };

      this.updateStatus(UpdateStatus.DOWNLOADING, this.currentUpdateInfo, progressInfo);
    });

    // Download complete
    Stallion.on('downloadComplete', () => {
      logger.info('[STALLION_OTA] Update download complete', null, 'stallionOTA');
      this.updateStatus(UpdateStatus.INSTALLING, this.currentUpdateInfo);
    });

    // Update installed
    Stallion.on('updateInstalled', () => {
      logger.info('[STALLION_OTA] Update installed successfully', null, 'stallionOTA');
      this.updateStatus(UpdateStatus.RESTART_REQUIRED, this.currentUpdateInfo);
    });

    // Update error
    Stallion.on('updateError', (error: any) => {
      logger.error('[STALLION_OTA] Update error occurred', error, 'stallionOTA');
      this.updateStatus(UpdateStatus.ERROR, this.currentUpdateInfo);
    });

    // No update available
    Stallion.on('upToDate', () => {
      logger.info('[STALLION_OTA] App is up to date', null, 'stallionOTA');
      this.updateStatus(UpdateStatus.UP_TO_DATE);
    });

    // Rollback occurred
    Stallion.on('rollback', (reason: string) => {
      logger.warn('[STALLION_OTA] Update rolled back', { reason }, 'stallionOTA');
      this.updateStatus(UpdateStatus.ERROR, null);
    });
  }

  /**
   * Set up app state listener for background/foreground update checks
   */
  private setupAppStateListener(): void {
    this.appStateSubscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active' && this.isInitialized) {
        // App came to foreground, check for updates
        this.checkForUpdates();
      }
    });
  }

  /**
   * Start periodic update checks
   */
  private startPeriodicChecks(): void {
    const config = require('../../stallion.config.js');
    
    if (config.checkInterval && config.checkInterval > 0) {
      this.checkInterval = setInterval(() => {
        this.checkForUpdates();
      }, config.checkInterval);
    }
  }

  /**
   * Check for available updates
   */
  async checkForUpdates(): Promise<UpdateInfo | null> {
    try {
      if (!this.isInitialized) {
        logger.warn('[STALLION_OTA] Service not initialized, skipping update check', null, 'stallionOTA');
        return null;
      }

      logger.info('[STALLION_OTA] Checking for updates...', null, 'stallionOTA');
      this.updateStatus(UpdateStatus.CHECKING);

      const updateInfo = await Stallion.checkForUpdate();
      
      if (updateInfo && updateInfo.available) {
        const info: UpdateInfo = {
          isAvailable: true,
          isMandatory: updateInfo.isMandatory || false,
          version: updateInfo.version,
          buildNumber: updateInfo.buildNumber,
          description: updateInfo.description,
          packageSize: updateInfo.packageSize,
        };

        this.currentUpdateInfo = info;
        this.updateStatus(UpdateStatus.UPDATE_AVAILABLE, info);
        
        return info;
      } else {
        this.updateStatus(UpdateStatus.UP_TO_DATE);
        return null;
      }
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to check for updates', error, 'stallionOTA');
      this.updateStatus(UpdateStatus.ERROR);
      return null;
    }
  }

  /**
   * Download available update
   */
  async downloadUpdate(): Promise<boolean> {
    try {
      if (!this.currentUpdateInfo?.isAvailable) {
        logger.warn('[STALLION_OTA] No update available to download', null, 'stallionOTA');
        return false;
      }

      logger.info('[STALLION_OTA] Starting update download...', null, 'stallionOTA');
      this.updateStatus(UpdateStatus.DOWNLOADING, this.currentUpdateInfo);

      await Stallion.downloadUpdate();
      return true;
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to download update', error, 'stallionOTA');
      this.updateStatus(UpdateStatus.ERROR);
      return false;
    }
  }

  /**
   * Install downloaded update
   */
  async installUpdate(): Promise<boolean> {
    try {
      logger.info('[STALLION_OTA] Installing update...', null, 'stallionOTA');
      this.updateStatus(UpdateStatus.INSTALLING, this.currentUpdateInfo);

      await Stallion.applyUpdate();
      return true;
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to install update', error, 'stallionOTA');
      this.updateStatus(UpdateStatus.ERROR);
      return false;
    }
  }

  /**
   * Download and install update in one step
   */
  async downloadAndInstall(): Promise<boolean> {
    const downloaded = await this.downloadUpdate();
    if (downloaded) {
      return await this.installUpdate();
    }
    return false;
  }

  /**
   * Restart app to apply update
   */
  async restartApp(): Promise<void> {
    try {
      logger.info('[STALLION_OTA] Restarting app to apply update...', null, 'stallionOTA');
      await Stallion.restartApp();
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to restart app', error, 'stallionOTA');
    }
  }

  /**
   * Get current update status
   */
  getStatus(): UpdateStatus {
    return this.currentStatus;
  }

  /**
   * Get current update info
   */
  getUpdateInfo(): UpdateInfo | null {
    return this.currentUpdateInfo;
  }

  /**
   * Add event listener for update events
   */
  addEventListener(listener: UpdateEventListener): void {
    this.eventListeners.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: UpdateEventListener): void {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  /**
   * Update status and notify listeners
   */
  private updateStatus(status: UpdateStatus, info?: UpdateInfo, progress?: UpdateProgress): void {
    this.currentStatus = status;
    
    // Notify all listeners
    this.eventListeners.forEach(listener => {
      try {
        listener(status, info, progress);
      } catch (error) {
        logger.error('[STALLION_OTA] Error in event listener', error, 'stallionOTA');
      }
    });
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }

    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }

    this.eventListeners = [];
    this.isInitialized = false;
  }
}

// Export singleton instance
export const stallionOTAService = new StallionOTAService();
export default stallionOTAService;
