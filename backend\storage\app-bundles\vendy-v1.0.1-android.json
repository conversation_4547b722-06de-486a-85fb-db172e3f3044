{"version": "1.0.1", "buildNumber": "2", "platform": "android", "bundleType": "javascript", "description": "Bug fixes and crash report improvements", "releaseNotes": ["Fixed crash report loop issue", "Improved session management", "Enhanced security features", "Better error handling"], "files": [{"path": "index.android.bundle", "size": 512000, "checksum": "sha256:a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"}, {"path": "assets/images/logo.png", "size": 25600, "checksum": "sha256:b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef12345678"}], "metadata": {"createdAt": "2025-01-22T17:00:00.000Z", "createdBy": "Vendy Build System", "buildEnvironment": "production", "reactNativeVersion": "0.72.0", "targetSdkVersion": 33, "minSdkVersion": 21}, "rollback": {"enabled": true, "previousVersion": "1.0.0", "rollbackInstructions": "Revert to previous bundle if installation fails"}}