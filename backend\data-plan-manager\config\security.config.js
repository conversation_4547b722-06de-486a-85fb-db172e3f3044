/**
 * Security Configuration for Data Plan Manager
 * 
 * Centralized security settings and utilities
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const crypto = require('crypto');
const logger = require('../../utils/logger');

class SecurityConfig {
  constructor() {
    this.encryptionKey = process.env.DATA_PLAN_ENCRYPTION_KEY || this.generateEncryptionKey();
    this.algorithm = 'aes-256-gcm';
    
    // Rate limiting configuration
    this.rateLimits = {
      authentication: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxAttempts: 5, // 5 attempts per window
        blockDuration: 30 * 60 * 1000 // 30 minutes block
      },
      apiCalls: {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: 60, // 60 requests per minute
        burstLimit: 10 // 10 requests in burst
      }
    };
    
    // Security headers
    this.securityHeaders = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    };
    
    // Sensitive data patterns for masking
    this.sensitivePatterns = {
      email: /(.{3}).*(@.*)/,
      token: /^(.{15}).*(.{10})$/,
      phone: /(\d{3})(\d{4})(\d{4})/,
      password: /.*/
    };
    
    // Failed attempt tracking
    this.failedAttempts = new Map();
    this.blockedIPs = new Map();
  }

  /**
   * Generate encryption key
   */
  generateEncryptionKey() {
    const key = crypto.randomBytes(32).toString('hex');
    logger.warn('⚠️ [SECURITY_CONFIG] Generated new encryption key. Please set DATA_PLAN_ENCRYPTION_KEY in environment variables');
    return key;
  }

  /**
   * Encrypt sensitive data
   */
  encrypt(text) {
    try {
      const iv = crypto.randomBytes(16);
      const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
      const cipher = crypto.createCipherGCM(this.algorithm, key, iv);
      cipher.setAAD(Buffer.from('data-plan-manager'));

      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      const authTag = cipher.getAuthTag();

      return {
        encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex')
      };
    } catch (error) {
      logger.error('❌ [SECURITY_CONFIG] Encryption failed:', { error: error.message });
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypt sensitive data
   */
  decrypt(encryptedData) {
    try {
      const { encrypted, iv, authTag } = encryptedData;
      const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
      const decipher = crypto.createDecipherGCM(this.algorithm, key, Buffer.from(iv, 'hex'));
      decipher.setAAD(Buffer.from('data-plan-manager'));
      decipher.setAuthTag(Buffer.from(authTag, 'hex'));

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      logger.error('❌ [SECURITY_CONFIG] Decryption failed:', { error: error.message });
      throw new Error('Decryption failed');
    }
  }

  /**
   * Mask sensitive data for logging
   */
  maskSensitiveData(data, type = 'general') {
    if (!data) return data;
    
    switch (type) {
      case 'email':
        return data.replace(this.sensitivePatterns.email, '$1***$2');
      case 'token':
        return data.length > 25 ? 
          data.substring(0, 15) + '...' + data.slice(-10) : 
          data.substring(0, 10) + '...';
      case 'phone':
        return data.replace(this.sensitivePatterns.phone, '$1***$3');
      case 'password':
        return '***';
      default:
        return data;
    }
  }

  /**
   * Validate request origin
   */
  validateRequestOrigin(req) {
    const allowedOrigins = [
      // Production domains
      'https://vendy.app',
      'https://admin.vendy.app',
      // Environment-based URLs
      process.env.FRONTEND_URL,
      process.env.ADMIN_URL,
      // Development URLs (only if in development)
      ...(process.env.NODE_ENV === 'development' ? [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:3002'
      ] : [])
    ].filter(Boolean);

    const origin = req.headers.origin || req.headers.referer;

    if (!origin) {
      // Allow requests without origin (mobile apps, Postman, etc.)
      return true;
    }

    return allowedOrigins.some(allowed => origin.startsWith(allowed));
  }

  /**
   * Rate limiting check
   */
  checkRateLimit(identifier, type = 'apiCalls') {
    const config = this.rateLimits[type];
    const now = Date.now();
    const windowStart = now - config.windowMs;
    
    // Clean old entries
    this.cleanOldEntries(windowStart);
    
    // Get current attempts
    const key = `${type}_${identifier}`;
    const attempts = this.failedAttempts.get(key) || [];
    const recentAttempts = attempts.filter(time => time > windowStart);
    
    // Check if blocked
    if (this.blockedIPs.has(identifier)) {
      const blockExpiry = this.blockedIPs.get(identifier);
      if (now < blockExpiry) {
        return {
          allowed: false,
          reason: 'IP blocked',
          retryAfter: Math.ceil((blockExpiry - now) / 1000)
        };
      } else {
        this.blockedIPs.delete(identifier);
      }
    }
    
    // Check rate limit
    const maxAttempts = config.maxAttempts || config.maxRequests;
    if (recentAttempts.length >= maxAttempts) {
      // Block IP if authentication attempts
      if (type === 'authentication') {
        this.blockedIPs.set(identifier, now + config.blockDuration);
      }
      
      return {
        allowed: false,
        reason: 'Rate limit exceeded',
        retryAfter: Math.ceil(config.windowMs / 1000)
      };
    }
    
    // Record this attempt
    recentAttempts.push(now);
    this.failedAttempts.set(key, recentAttempts);
    
    return {
      allowed: true,
      remaining: maxAttempts - recentAttempts.length
    };
  }

  /**
   * Clean old rate limit entries
   */
  cleanOldEntries(windowStart) {
    for (const [key, attempts] of this.failedAttempts.entries()) {
      const recentAttempts = attempts.filter(time => time > windowStart);
      if (recentAttempts.length === 0) {
        this.failedAttempts.delete(key);
      } else {
        this.failedAttempts.set(key, recentAttempts);
      }
    }
  }

  /**
   * Sanitize input data
   */
  sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    // Remove potentially dangerous characters
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }

  /**
   * Generate secure request ID
   */
  generateSecureRequestId() {
    return `dpm_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  }

  /**
   * Validate API key format
   */
  validateApiKeyFormat(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
      return false;
    }
    
    // Check if it looks like a JWT token
    const jwtPattern = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
    return jwtPattern.test(apiKey);
  }

  /**
   * Get security headers
   */
  getSecurityHeaders() {
    return { ...this.securityHeaders };
  }

  /**
   * Log security event
   */
  logSecurityEvent(event, details = {}) {
    logger.warn(`🔒 [SECURITY_EVENT] ${event}`, {
      timestamp: new Date().toISOString(),
      event,
      ...details
    });
  }

  /**
   * Check if IP is blocked
   */
  isIPBlocked(ip) {
    if (this.blockedIPs.has(ip)) {
      const blockExpiry = this.blockedIPs.get(ip);
      if (Date.now() < blockExpiry) {
        return true;
      } else {
        this.blockedIPs.delete(ip);
      }
    }
    return false;
  }

  /**
   * Get security status
   */
  getSecurityStatus() {
    return {
      blockedIPs: this.blockedIPs.size,
      activeRateLimits: this.failedAttempts.size,
      encryptionEnabled: !!this.encryptionKey,
      securityHeadersEnabled: Object.keys(this.securityHeaders).length > 0
    };
  }
}

// Create and export singleton instance
const securityConfig = new SecurityConfig();

module.exports = securityConfig;
