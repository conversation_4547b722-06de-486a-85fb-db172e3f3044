/**
 * Data Plans API Routes
 * 
 * RESTful API endpoints for data plan management
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const dataPlanManager = require('../data-plan-manager/services/dataPlanManager');
const cacheManager = require('../data-plan-manager/services/cacheManager');
const logger = require('../utils/logger');

// Middleware for admin authentication
const requireAdmin = (req, res, next) => {
  // Add your admin authentication logic here
  // For now, we'll skip authentication in development
  if (process.env.NODE_ENV === 'development') {
    return next();
  }
  
  // Add JWT verification for production
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  
  // Verify JWT token here
  next();
};

// ============================================================================
// Public Data Plans Endpoints
// ============================================================================

/**
 * GET /api/data-plans/test
 * Simple test endpoint
 */
router.get('/test', async (req, res) => {
  try {
    logger.info('🧪 [API] Test endpoint called');

    res.json({
      success: true,
      message: 'Data Plans API is working',
      timestamp: new Date().toISOString(),
      dataPlanManagerAvailable: !!dataPlanManager
    });

  } catch (error) {
    logger.error('❌ [API] Test endpoint failed:', error);
    res.status(500).json({
      success: false,
      error: 'Test endpoint failed',
      details: error.message
    });
  }
});

/**
 * GET /api/data-plans
 * Get all available data plans
 */
router.get('/', async (req, res) => {
  try {
    logger.info('📊 [API] Fetching all data plans');
    logger.info('📊 [API] Data plan manager available:', !!dataPlanManager);

    if (!dataPlanManager) {
      throw new Error('Data plan manager not available');
    }

    logger.info('📊 [API] Calling getAllPlans...');
    const plans = await dataPlanManager.getAllPlans();
    logger.info('📊 [API] Plans retrieved:', Object.keys(plans || {}).length);

    logger.info('📊 [API] Calling getStatus...');
    const status = await dataPlanManager.getStatus();
    logger.info('📊 [API] Status retrieved:', !!status);

    // Calculate total plans safely
    let totalPlans = 0;
    try {
      totalPlans = Object.values(plans).reduce((sum, vendor) => {
        if (vendor && vendor.networks) {
          return sum + Object.values(vendor.networks).reduce((netSum, network) =>
            netSum + (Array.isArray(network.plans) ? network.plans.length : 0), 0);
        }
        return sum + (vendor.totalPlans || 0);
      }, 0);
    } catch (countError) {
      logger.warn('⚠️ [API] Error counting total plans:', countError);
      totalPlans = 0;
    }

    res.json({
      success: true,
      data: {
        vendors: plans,
        lastUpdated: status.lastUpdate || new Date().toISOString(),
        totalPlans: totalPlans
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [API] Failed to fetch data plans:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch data plans',
      details: error.message
    });
  }
});

/**
 * GET /api/data-plans/network/:network
 * Get plans for specific network
 */
router.get('/network/:network', async (req, res) => {
  try {
    const { network } = req.params;
    logger.info(`📱 [API] Fetching plans for network: ${network}`);
    
    const plans = await dataPlanManager.getPlansByNetwork(network.toUpperCase());
    
    res.json({
      success: true,
      data: plans,
      network: network.toUpperCase(),
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error(`❌ [API] Failed to fetch plans for network ${req.params.network}:`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch network plans'
    });
  }
});

/**
 * GET /api/data-plans/cheapest/:network/:size
 * Get cheapest plan for network and size
 */
router.get('/cheapest/:network/:size', async (req, res) => {
  try {
    const { network, size } = req.params;
    logger.info(`💰 [API] Finding cheapest plan: ${network} ${size}`);
    
    const plan = await dataPlanManager.getCheapestPlan(network.toUpperCase(), size);
    
    if (!plan) {
      return res.status(404).json({
        success: false,
        error: 'No plans found for the specified criteria'
      });
    }
    
    res.json({
      success: true,
      data: plan,
      network: network.toUpperCase(),
      size: size,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error(`❌ [API] Failed to find cheapest plan:`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to find cheapest plan'
    });
  }
});

/**
 * GET /api/data-plans/status
 * Get system status
 */
router.get('/status', async (req, res) => {
  try {
    logger.info('🔍 [API] Fetching system status');

    const managerStatus = await dataPlanManager.getStatus();

    // Try to get cache health, but don't fail if it's not available
    let cacheHealth = { status: 'unknown' };
    try {
      if (cacheManager && typeof cacheManager.healthCheck === 'function') {
        cacheHealth = await cacheManager.healthCheck();
      }
    } catch (cacheError) {
      logger.warn('⚠️ [API] Cache health check failed:', cacheError);
      cacheHealth = { status: 'error', error: cacheError.message };
    }

    res.json({
      success: true,
      data: {
        manager: managerStatus,
        realtime: cacheHealth,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('❌ [API] Failed to fetch status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch system status',
      details: error.message
    });
  }
});

// ============================================================================
// Admin Data Plans Endpoints
// ============================================================================

/**
 * POST /api/data-plans/admin/force-update
 * Force update all vendors
 */
router.post('/admin/force-update', requireAdmin, async (req, res) => {
  try {
    logger.info('🔄 [API] Force updating all vendors');
    
    const result = await dataPlanManager.forceUpdateAll();
    
    res.json({
      success: true,
      data: {
        message: 'Force update initiated for all vendors',
        vendors: result.vendors,
        updated: result.updated
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('❌ [API] Force update failed:', error);
    res.status(500).json({
      success: false,
      error: 'Force update failed'
    });
  }
});

/**
 * POST /api/data-plans/admin/force-update/:vendorId
 * Force update specific vendor
 */
router.post('/admin/force-update/:vendorId', requireAdmin, async (req, res) => {
  try {
    const { vendorId } = req.params;
    logger.info(`🔄 [API] Force updating vendor: ${vendorId}`);
    
    const result = await dataPlanManager.forceUpdateVendor(vendorId);
    
    res.json({
      success: true,
      data: {
        message: `Vendor ${vendorId} updated successfully`,
        vendor: vendorId,
        plans: result.plans,
        lastUpdated: result.lastUpdated
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error(`❌ [API] Vendor ${req.params.vendorId} update failed:`, error);
    res.status(500).json({
      success: false,
      error: `Failed to update vendor ${req.params.vendorId}`
    });
  }
});

/**
 * GET /api/data-plans/admin/vendors
 * Get all vendors configuration
 */
router.get('/admin/vendors', requireAdmin, async (req, res) => {
  try {
    logger.info('🏢 [API] Fetching vendors configuration');
    
    const vendors = await dataPlanManager.getVendorsConfig();
    
    res.json({
      success: true,
      data: vendors,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('❌ [API] Failed to fetch vendors:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch vendors configuration'
    });
  }
});

/**
 * PUT /api/data-plans/admin/vendors/:vendorId
 * Update vendor configuration
 */
router.put('/admin/vendors/:vendorId', requireAdmin, async (req, res) => {
  try {
    const { vendorId } = req.params;
    const vendorData = req.body;
    
    logger.info(`🔧 [API] Updating vendor configuration: ${vendorId}`);
    
    const result = await dataPlanManager.updateVendorConfig(vendorId, vendorData);
    
    res.json({
      success: true,
      data: {
        message: `Vendor ${vendorId} configuration updated`,
        vendor: result
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error(`❌ [API] Failed to update vendor ${req.params.vendorId}:`, error);
    res.status(500).json({
      success: false,
      error: `Failed to update vendor configuration`
    });
  }
});

/**
 * DELETE /api/data-plans/admin/cache
 * Clear all cache
 */
router.delete('/admin/cache', requireAdmin, async (req, res) => {
  try {
    logger.info('🗑️ [API] Clearing data plans cache');
    
    await cacheManager.clear();
    await dataPlanManager.clearCache();
    
    res.json({
      success: true,
      data: {
        message: 'Cache cleared successfully'
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('❌ [API] Failed to clear cache:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear cache'
    });
  }
});

/**
 * GET /api/data-plans/admin/pricing/history
 * Get price history
 */
router.get('/admin/pricing/history', requireAdmin, async (req, res) => {
  try {
    const { limit = 50 } = req.query;
    logger.info(`📈 [API] Fetching price history (limit: ${limit})`);

    // For now, return empty array - implement actual price tracking later
    const history = [];

    res.json({
      success: true,
      data: history,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [API] Failed to fetch price history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch price history'
    });
  }
});

/**
 * PUT /api/data-plans/admin/pricing/config
 * Update pricing configuration
 */
router.put('/admin/pricing/config', requireAdmin, async (req, res) => {
  try {
    const config = req.body;
    logger.info('💰 [API] Updating pricing configuration');

    // For now, just return success - implement actual config storage later
    res.json({
      success: true,
      data: {
        message: 'Pricing configuration updated successfully',
        config
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [API] Failed to update pricing config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update pricing configuration'
    });
  }
});

/**
 * GET /api/data-plans/admin/security/config
 * Get security configuration
 */
router.get('/admin/security/config', requireAdmin, async (req, res) => {
  try {
    logger.info('🔒 [API] Fetching security configuration');

    // Return default security config
    const config = {
      enableRateLimit: true,
      apiRateLimit: 60,
      authRateLimit: 5,
      enableIPBlocking: true,
      enableEncryption: true,
      enableSecurityHeaders: true,
      enableCORS: true,
      allowedOrigins: ['http://localhost:3000'],
      jwtExpiry: '1h',
      encryptionKey: ''
    };

    res.json({
      success: true,
      data: config,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [API] Failed to fetch security config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch security configuration'
    });
  }
});

/**
 * PUT /api/data-plans/admin/security/config
 * Update security configuration
 */
router.put('/admin/security/config', requireAdmin, async (req, res) => {
  try {
    const config = req.body;
    logger.info('🔒 [API] Updating security configuration');

    // For now, just return success - implement actual config storage later
    res.json({
      success: true,
      data: {
        message: 'Security configuration updated successfully',
        config
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [API] Failed to update security config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update security configuration'
    });
  }
});

/**
 * GET /api/data-plans/admin/security/blocked-ips
 * Get blocked IPs
 */
router.get('/admin/security/blocked-ips', requireAdmin, async (req, res) => {
  try {
    logger.info('🚫 [API] Fetching blocked IPs');

    // For now, return empty array - implement actual IP blocking later
    const blockedIPs = [];

    res.json({
      success: true,
      data: blockedIPs,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [API] Failed to fetch blocked IPs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch blocked IPs'
    });
  }
});

/**
 * POST /api/data-plans/admin/security/block-ip
 * Block IP address
 */
router.post('/admin/security/block-ip', requireAdmin, async (req, res) => {
  try {
    const { ipAddress, reason } = req.body;
    logger.info(`🚫 [API] Blocking IP: ${ipAddress}`);

    // For now, just return success - implement actual IP blocking later
    res.json({
      success: true,
      data: {
        message: `IP ${ipAddress} blocked successfully`,
        ipAddress,
        reason: reason || 'Manual block'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [API] Failed to block IP:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to block IP address'
    });
  }
});

/**
 * DELETE /api/data-plans/admin/security/block-ip/:ip
 * Unblock IP address
 */
router.delete('/admin/security/block-ip/:ip', requireAdmin, async (req, res) => {
  try {
    const { ip } = req.params;
    logger.info(`✅ [API] Unblocking IP: ${ip}`);

    // For now, just return success - implement actual IP unblocking later
    res.json({
      success: true,
      data: {
        message: `IP ${ip} unblocked successfully`,
        ipAddress: ip
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [API] Failed to unblock IP:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to unblock IP address'
    });
  }
});

/**
 * GET /api/data-plans/admin/security/logs
 * Get security logs
 */
router.get('/admin/security/logs', requireAdmin, async (req, res) => {
  try {
    const { limit = 50 } = req.query;
    logger.info(`📋 [API] Fetching security logs (limit: ${limit})`);

    // For now, return empty array - implement actual security logging later
    const logs = [];

    res.json({
      success: true,
      data: logs,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [API] Failed to fetch security logs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch security logs'
    });
  }
});

/**
 * GET /api/data-plans/admin/security/rate-limits
 * Get current rate limits
 */
router.get('/admin/security/rate-limits', requireAdmin, async (req, res) => {
  try {
    logger.info('⏱️ [API] Fetching rate limits');

    // For now, return empty array - implement actual rate limiting later
    const rateLimits = [];

    res.json({
      success: true,
      data: rateLimits,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [API] Failed to fetch rate limits:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch rate limits'
    });
  }
});

module.exports = router;
