/**
 * OTA Bundle Management Utility
 * 
 * Provides utilities for managing OTA update bundles
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const logger = require('./logger');

class OTAManager {
  constructor() {
    this.configPath = path.join(__dirname, '../config/app-updates.json');
    this.bundlesPath = path.join(__dirname, '../storage/app-bundles');
  }

  /**
   * Load current OTA configuration
   */
  async loadConfig() {
    try {
      const configData = await fs.readFile(this.configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      logger.error('Failed to load OTA config:', error);
      throw error;
    }
  }

  /**
   * Save OTA configuration
   */
  async saveConfig(config) {
    try {
      await fs.writeFile(this.configPath, JSON.stringify(config, null, 2));
      logger.info('OTA config saved successfully');
    } catch (error) {
      logger.error('Failed to save OTA config:', error);
      throw error;
    }
  }

  /**
   * Calculate file checksum
   */
  async calculateChecksum(filePath) {
    try {
      const fileBuffer = await fs.readFile(filePath);
      const hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      return `sha256:${hash}`;
    } catch (error) {
      logger.error('Failed to calculate checksum:', error);
      throw error;
    }
  }

  /**
   * Add new update bundle
   */
  async addUpdate(updateData, bundleFilePath) {
    try {
      const config = await this.loadConfig();
      
      // Calculate checksum and size
      const checksum = await this.calculateChecksum(bundleFilePath);
      const stats = await fs.stat(bundleFilePath);
      
      // Generate update ID
      const updateId = `vendy-v${updateData.version}-${updateData.platform}`;
      
      // Create update entry
      const newUpdate = {
        id: updateId,
        version: updateData.version,
        buildNumber: updateData.buildNumber,
        platform: updateData.platform,
        filename: path.basename(bundleFilePath),
        downloadUrl: `/api/v1/app/updates/download/${updateId}`,
        checksum,
        size: stats.size,
        isMandatory: updateData.isMandatory || false,
        description: updateData.description,
        releaseNotes: updateData.releaseNotes || '',
        minAppVersion: updateData.minAppVersion || null,
        maxAppVersion: updateData.maxAppVersion || null,
        createdAt: new Date().toISOString(),
        deployedAt: new Date().toISOString(),
        isActive: true
      };

      // Add to config
      config.updates.push(newUpdate);
      config.lastUpdated = new Date().toISOString();

      // Save config
      await this.saveConfig(config);

      logger.info('New OTA update added:', {
        updateId,
        version: updateData.version,
        platform: updateData.platform,
        size: stats.size
      });

      return newUpdate;
    } catch (error) {
      logger.error('Failed to add OTA update:', error);
      throw error;
    }
  }

  /**
   * Remove update bundle
   */
  async removeUpdate(updateId) {
    try {
      const config = await this.loadConfig();
      
      const updateIndex = config.updates.findIndex(u => u.id === updateId);
      if (updateIndex === -1) {
        throw new Error('Update not found');
      }

      const update = config.updates[updateIndex];
      
      // Remove from config
      config.updates.splice(updateIndex, 1);
      config.lastUpdated = new Date().toISOString();

      // Save config
      await this.saveConfig(config);

      logger.info('OTA update removed:', { updateId });

      return update;
    } catch (error) {
      logger.error('Failed to remove OTA update:', error);
      throw error;
    }
  }

  /**
   * Get update by ID
   */
  async getUpdate(updateId) {
    try {
      const config = await this.loadConfig();
      return config.updates.find(u => u.id === updateId);
    } catch (error) {
      logger.error('Failed to get OTA update:', error);
      throw error;
    }
  }

  /**
   * Find applicable update for given version and platform
   */
  async findApplicableUpdate(currentVersion, currentBuildNumber, platform) {
    try {
      const config = await this.loadConfig();
      
      if (!config.config.enableUpdates) {
        return null;
      }

      const updates = config.updates || [];
      
      // Filter updates for the platform and active status
      const platformUpdates = updates.filter(update => 
        (update.platform === platform || update.platform === 'both') &&
        update.isActive
      );

      // Sort by version (newest first)
      platformUpdates.sort((a, b) => {
        const versionA = a.version.split('.').map(Number);
        const versionB = b.version.split('.').map(Number);
        
        for (let i = 0; i < Math.max(versionA.length, versionB.length); i++) {
          const numA = versionA[i] || 0;
          const numB = versionB[i] || 0;
          if (numA !== numB) return numB - numA;
        }
        
        return parseInt(b.buildNumber) - parseInt(a.buildNumber);
      });

      // Find the first update that's newer than current version
      for (const update of platformUpdates) {
        const updateVersion = update.version.split('.').map(Number);
        const currentVersionParts = currentVersion.split('.').map(Number);
        
        let isNewer = false;
        
        // Compare version numbers
        for (let i = 0; i < Math.max(updateVersion.length, currentVersionParts.length); i++) {
          const updatePart = updateVersion[i] || 0;
          const currentPart = currentVersionParts[i] || 0;
          
          if (updatePart > currentPart) {
            isNewer = true;
            break;
          } else if (updatePart < currentPart) {
            break;
          }
        }
        
        // If versions are equal, compare build numbers
        if (!isNewer && update.version === currentVersion) {
          isNewer = parseInt(update.buildNumber) > parseInt(currentBuildNumber);
        }
        
        if (isNewer) {
          // Check version compatibility
          if (update.minAppVersion) {
            const minVersion = update.minAppVersion.split('.').map(Number);
            let isCompatible = true;
            
            for (let i = 0; i < Math.max(minVersion.length, currentVersionParts.length); i++) {
              const minPart = minVersion[i] || 0;
              const currentPart = currentVersionParts[i] || 0;
              
              if (currentPart < minPart) {
                isCompatible = false;
                break;
              } else if (currentPart > minPart) {
                break;
              }
            }
            
            if (!isCompatible) continue;
          }
          
          if (update.maxAppVersion) {
            const maxVersion = update.maxAppVersion.split('.').map(Number);
            let isCompatible = true;
            
            for (let i = 0; i < Math.max(maxVersion.length, currentVersionParts.length); i++) {
              const maxPart = maxVersion[i] || 0;
              const currentPart = currentVersionParts[i] || 0;
              
              if (currentPart > maxPart) {
                isCompatible = false;
                break;
              } else if (currentPart < maxPart) {
                break;
              }
            }
            
            if (!isCompatible) continue;
          }
          
          return update;
        }
      }
      
      return null;
    } catch (error) {
      logger.error('Failed to find applicable update:', error);
      throw error;
    }
  }

  /**
   * Get bundle file path
   */
  getBundlePath(filename) {
    return path.join(this.bundlesPath, filename);
  }

  /**
   * Verify bundle file exists
   */
  async verifyBundleExists(filename) {
    try {
      const bundlePath = this.getBundlePath(filename);
      await fs.access(bundlePath);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get OTA statistics
   */
  async getStats() {
    try {
      const config = await this.loadConfig();
      
      const stats = {
        totalUpdates: config.updates.length,
        activeUpdates: config.updates.filter(u => u.isActive).length,
        platforms: [...new Set(config.updates.map(u => u.platform))],
        latestVersions: {},
        totalSize: 0
      };

      // Calculate latest versions per platform
      for (const platform of stats.platforms) {
        const platformUpdates = config.updates
          .filter(u => u.platform === platform && u.isActive)
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        if (platformUpdates.length > 0) {
          stats.latestVersions[platform] = platformUpdates[0].version;
        }
      }

      // Calculate total size
      stats.totalSize = config.updates.reduce((sum, update) => sum + (update.size || 0), 0);

      return stats;
    } catch (error) {
      logger.error('Failed to get OTA stats:', error);
      throw error;
    }
  }
}

module.exports = new OTAManager();
